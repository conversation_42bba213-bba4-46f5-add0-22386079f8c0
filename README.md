# use-k8s-mcp

Kubernetes MCP Agent with configurable LLM providers

## 📁 项目结构

```
use-k8s-mcp/
├── src/                    # 源代码
│   ├── llm_config.py      # 核心LLM配置模块
│   ├── main.py            # 主程序
│   ├── k8s_config.py      # Kubernetes配置
│   ├── k8s_example.py     # Kubernetes示例
│   └── config_examples.py # 配置示例
├── test/                   # 测试和工具
│   ├── test_llm_config.py # LLM配置测试
│   ├── demo_llm_config.py # 演示脚本
│   └── switch_provider.py # 提供商切换工具
├── doc/                    # 文档
│   ├── README.md          # 详细说明文档
│   ├── LLM_PROVIDER_GUIDE.md # 使用指南
│   ├── IMPLEMENTATION_SUMMARY.md # 实现总结
│   └── TROUBLESHOOTING.md # 故障排除
├── script/                 # 部署脚本
│   └── deploy-vllm.sh     # VLLM部署脚本
├── .env                    # 环境配置
├── main.py                 # 主程序入口点
├── run_tests.py           # 测试运行器
├── switch_provider.py     # 提供商切换入口点
└── pyproject.toml         # 项目配置
```

## 🚀 快速开始

### 1. 安装依赖
```bash
uv sync
```

### 2. 配置环境
编辑 `.env` 文件，选择LLM提供商：
```bash
# 选择提供商: "openrouter" 或 "qwen3"
LLM_PROVIDER=qwen3
```

### 3. 运行程序
```bash
# 运行主程序
uv run python main.py

# 或者使用 uv 脚本
uv run use-k8s-mcp
```

### 4. 切换提供商
```bash
# 切换到OpenRouter
uv run python switch_provider.py openrouter

# 切换到Qwen3
uv run python switch_provider.py qwen3

# 查看状态
uv run python switch_provider.py status
```

### 5. 运行测试
```bash
# 运行配置测试
uv run python run_tests.py

# 或者直接运行测试文件
uv run python test/test_llm_config.py
```

## 📚 文档

详细文档请查看 `doc/` 目录：

- **[详细说明](doc/README.md)** - 完整的项目说明
- **[使用指南](doc/LLM_PROVIDER_GUIDE.md)** - LLM提供商配置指南
- **[实现总结](doc/IMPLEMENTATION_SUMMARY.md)** - 技术实现总结
- **[故障排除](doc/TROUBLESHOOTING.md)** - 常见问题解决

## 🔧 开发

### 项目结构说明

- **`src/`** - 所有源代码文件
- **`test/`** - 测试文件和工具脚本
- **`doc/`** - 项目文档
- **`script/`** - 部署和运维脚本

### 运行测试

```bash
# 运行所有测试
uv run python run_tests.py

# 运行演示
uv run python test/demo_llm_config.py
```

### 添加新功能

1. 在 `src/` 目录添加源代码
2. 在 `test/` 目录添加测试
3. 更新 `doc/` 目录中的相关文档

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
