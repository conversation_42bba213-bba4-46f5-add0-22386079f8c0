#!/usr/bin/env python3
"""
Gemini 2.5 Flash 大上下文在K8s运维中的实际应用示例
展示如何利用1,048,576 tokens输入上下文处理复杂的K8s运维任务
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from mcp_use import MCPAgent, MCPClient
from src.llm_config import create_large_context_llm, get_context_info


async def large_context_log_analysis_example():
    """
    示例1: 大量日志分析
    利用大上下文能力分析多个Pod的完整日志
    """
    print("📋 示例1: 大量日志分析")
    print("=" * 50)
    
    # MCP配置
    config = {
        "mcpServers": {
            "k8s": {
                "type": "sse",
                "url": "http://ncpdev.gf.com.cn:31455/sse"
            }
        }
    }
    
    # 创建大上下文LLM (32K输出，支持1M+输入)
    llm = create_large_context_llm(max_tokens=16384)
    client = MCPClient.from_dict(config)
    agent = MCPAgent(llm=llm, client=client, max_steps=10)
    
    # 复杂的日志分析查询 - 可以处理大量日志数据
    complex_log_query = """
    请帮我进行一个全面的Pod日志分析：
    
    1. 获取gfxc-dev1集群中default命名空间的所有Pod
    2. 对于每个Pod，获取最近的日志（如果可能的话）
    3. 分析所有日志中的：
       - 错误模式和频率
       - 性能瓶颈指标
       - 异常行为模式
       - 资源使用警告
    4. 生成一个综合的日志分析报告，包括：
       - 问题优先级排序
       - 根本原因分析
       - 修复建议
       - 预防措施
    
    请提供详细的分析，包括具体的日志片段和解释。
    """
    
    print("🔍 执行大量日志分析...")
    print(f"📏 查询长度: {len(complex_log_query)} 字符")
    print(f"🎯 预期处理: 可能包含数万行日志数据")
    
    try:
        result = await agent.run(complex_log_query, max_steps=8)
        
        print("✅ 日志分析完成")
        print(f"📊 结果长度: {len(result):,} 字符")
        print(f"📋 结果预览:")
        print("-" * 40)
        print(result[:500] + "..." if len(result) > 500 else result)
        print("-" * 40)
        
        return True
    except Exception as e:
        print(f"❌ 日志分析失败: {e}")
        return False


async def large_context_config_comparison_example():
    """
    示例2: 复杂配置对比
    利用大上下文能力对比多个集群的完整配置
    """
    print("\n📋 示例2: 复杂配置对比")
    print("=" * 50)
    
    config = {
        "mcpServers": {
            "k8s": {
                "type": "sse", 
                "url": "http://ncpdev.gf.com.cn:31455/sse"
            }
        }
    }
    
    # 创建大上下文LLM
    llm = create_large_context_llm(max_tokens=20480)
    client = MCPClient.from_dict(config)
    agent = MCPAgent(llm=llm, client=client, max_steps=12)
    
    # 复杂的配置对比查询
    config_comparison_query = """
    请进行一个全面的多集群配置对比分析：
    
    1. 获取以下集群的详细信息：
       - gfxc-dev1
       - nmcp-dev-pm  
       - nmcp-dev-ctrl
    
    2. 对于每个集群，收集：
       - 基础配置信息
       - 网络配置
       - 安全设置
       - 资源配置
       - 版本信息
    
    3. 进行深度对比分析：
       - 配置一致性检查
       - 安全配置差异
       - 性能配置对比
       - 版本兼容性分析
       - 最佳实践合规性
    
    4. 生成详细的对比报告：
       - 配置差异矩阵
       - 风险评估
       - 标准化建议
       - 迁移计划（如需要）
    
    请提供一个可执行的标准化方案。
    """
    
    print("🔍 执行复杂配置对比...")
    print(f"📏 查询长度: {len(config_comparison_query)} 字符")
    print(f"🎯 预期处理: 多个集群的完整配置数据")
    
    try:
        result = await agent.run(config_comparison_query, max_steps=10)
        
        print("✅ 配置对比完成")
        print(f"📊 结果长度: {len(result):,} 字符")
        print(f"📋 结果预览:")
        print("-" * 40)
        print(result[:500] + "..." if len(result) > 500 else result)
        print("-" * 40)
        
        return True
    except Exception as e:
        print(f"❌ 配置对比失败: {e}")
        return False


async def demonstrate_context_advantages():
    """
    演示大上下文的具体优势
    """
    print("\n💡 大上下文优势演示")
    print("=" * 50)
    
    context_info = get_context_info()
    
    print("🎯 Gemini 2.5 Flash 大上下文能力:")
    print(f"   📏 输入上下文: {context_info['input_context_length']:,} tokens")
    print(f"   📤 最大输出: {max(context_info['output_configurations'].values()):,} tokens")
    print()
    
    print("🔥 实际应用优势:")
    advantages = [
        {
            "场景": "Pod日志分析",
            "传统方式": "需要分批处理，可能丢失上下文关联",
            "大上下文方式": "一次性处理所有日志，保持完整上下文",
            "输入规模": "~500K tokens"
        },
        {
            "场景": "多集群配置对比",
            "传统方式": "逐个集群分析，手动整合结果",
            "大上下文方式": "同时分析所有集群，自动生成对比报告",
            "输入规模": "~300K tokens"
        },
        {
            "场景": "全集群健康检查",
            "传统方式": "分模块检查，可能遗漏关联问题",
            "大上下文方式": "全局视角分析，发现隐藏关联",
            "输入规模": "~800K tokens"
        },
        {
            "场景": "复杂故障排查",
            "传统方式": "分步骤收集信息，可能中断思路",
            "大上下文方式": "一次性分析所有诊断数据",
            "输入规模": "~400K tokens"
        }
    ]
    
    for i, advantage in enumerate(advantages, 1):
        print(f"{i}. {advantage['场景']} ({advantage['输入规模']})")
        print(f"   ❌ 传统方式: {advantage['传统方式']}")
        print(f"   ✅ 大上下文: {advantage['大上下文方式']}")
        print()
    
    print("🚀 关键优势总结:")
    print("   - 保持完整的上下文关联")
    print("   - 避免信息分割导致的遗漏")
    print("   - 提供全局视角的分析")
    print("   - 减少多轮交互的复杂性")
    print("   - 提高分析的准确性和深度")


async def main():
    """主演示函数"""
    print("🚀 Gemini 2.5 Flash 大上下文K8s运维实战演示")
    print("=" * 70)
    
    # 加载环境变量
    load_dotenv()
    
    # 演示大上下文优势
    await demonstrate_context_advantages()
    
    # 运行实际示例
    examples = [
        ("大量日志分析示例", large_context_log_analysis_example),
        ("复杂配置对比示例", large_context_config_comparison_example),
    ]
    
    passed = 0
    total = len(examples)
    
    for example_name, example_func in examples:
        print(f"\n🧪 运行 {example_name}...")
        try:
            if await example_func():
                passed += 1
                print(f"✅ {example_name} 成功")
            else:
                print(f"❌ {example_name} 失败")
        except Exception as e:
            print(f"❌ {example_name} 异常: {e}")
    
    print("\n" + "=" * 70)
    print(f"🏁 演示完成: {passed}/{total} 成功")
    
    if passed == total:
        print("🎉 大上下文能力演示成功!")
        print("\n💡 实际应用建议:")
        print("   - 对于复杂分析任务，使用 create_large_context_llm()")
        print("   - 设置足够的 max_tokens 以获得完整输出")
        print("   - 增加 request_timeout 以处理大数据量")
        print("   - 利用大上下文避免信息分割")
    else:
        print("⚠️  部分演示未成功，请检查MCP连接")


if __name__ == "__main__":
    asyncio.run(main())
