#!/usr/bin/env python3
"""
LLM配置测试脚本
用于验证不同提供商的配置是否正确
"""

import os
import sys
from dotenv import load_dotenv
from llm_config import (
    create_llm, 
    create_k8s_llm, 
    get_current_provider, 
    get_provider_info,
    print_provider_status,
    llm_config
)


def test_provider_detection():
    """测试提供商检测"""
    print("🔍 测试提供商检测...")
    provider = get_current_provider()
    print(f"   当前提供商: {provider}")
    
    if provider not in ["openrouter", "qwen3"]:
        print(f"❌ 错误: 不支持的提供商 {provider}")
        return False
    
    print("✅ 提供商检测正常")
    return True


def test_provider_info():
    """测试提供商信息获取"""
    print("\n📋 测试提供商信息...")
    try:
        info = get_provider_info()
        print(f"   提供商: {info.get('provider', 'Unknown')}")
        print(f"   服务地址: {info.get('base_url', 'N/A')}")
        
        if llm_config.provider == "qwen3":
            print(f"   模型名称: {info.get('model_name', 'N/A')}")
            print(f"   思考模式: {info.get('thinking_mode', 'N/A')}")
        
        print("✅ 提供商信息获取正常")
        return True
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def test_llm_creation():
    """测试LLM实例创建"""
    print("\n🤖 测试LLM实例创建...")
    
    test_cases = [
        ("default", "默认配置"),
        ("production", "生产环境"),
        ("development", "开发环境"),
        ("analysis", "分析任务")
    ]
    
    success_count = 0
    
    for model_type, description in test_cases:
        try:
            print(f"   测试 {description} ({model_type})...")
            llm = create_llm(model_type=model_type)
            
            # 验证基本属性
            assert hasattr(llm, 'model_name'), "缺少model_name属性"
            assert hasattr(llm, 'temperature'), "缺少temperature属性"
            assert hasattr(llm, 'max_tokens'), "缺少max_tokens属性"
            assert hasattr(llm, 'openai_api_base'), "缺少openai_api_base属性"

            print(f"     ✅ {description}: {llm.model_name}")
            success_count += 1
            
        except Exception as e:
            print(f"     ❌ {description}: {e}")
    
    print(f"\n📊 LLM创建测试结果: {success_count}/{len(test_cases)} 成功")
    return success_count == len(test_cases)


def test_k8s_llm_creation():
    """测试Kubernetes专用LLM创建"""
    print("\n⚙️  测试Kubernetes专用LLM创建...")
    
    k8s_environments = [
        ("production", "生产环境"),
        ("development", "开发环境"), 
        ("analysis", "分析环境")
    ]
    
    success_count = 0
    
    for env, description in k8s_environments:
        try:
            print(f"   测试K8s {description} ({env})...")
            llm = create_k8s_llm(environment=env)
            
            # 验证K8s特定配置
            assert llm.temperature <= 0.1, f"温度过高: {llm.temperature}"
            assert llm.max_tokens >= 3000, f"最大token过少: {llm.max_tokens}"
            assert llm.stop, "缺少安全停止序列"
            
            print(f"     ✅ K8s {description}: 温度={llm.temperature}, Token={llm.max_tokens}")
            success_count += 1
            
        except Exception as e:
            print(f"     ❌ K8s {description}: {e}")
    
    print(f"\n📊 K8s LLM创建测试结果: {success_count}/{len(k8s_environments)} 成功")
    return success_count == len(k8s_environments)


def test_environment_variables():
    """测试环境变量配置"""
    print("\n🔧 测试环境变量配置...")
    
    provider = get_current_provider()
    missing_vars = []
    
    if provider == "openrouter":
        required_vars = ["OPENROUTER_API_KEY"]
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
    
    elif provider == "qwen3":
        required_vars = ["QWEN3_MODEL_SERVER"]
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        # 检查可选变量
        optional_vars = {
            "QWEN3_API_KEY": "EMPTY",
            "QWEN3_MODEL_NAME": "coder", 
            "QWEN3_ENABLE_THINKING": "false"
        }
        
        for var, default in optional_vars.items():
            value = os.getenv(var, default)
            print(f"   {var}: {value}")
    
    if missing_vars:
        print(f"❌ 缺少必需的环境变量: {', '.join(missing_vars)}")
        return False
    
    print("✅ 环境变量配置正常")
    return True


def main():
    """主测试函数"""
    print("🧪 LLM配置测试开始")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    # 显示当前状态
    print_provider_status()
    print("=" * 60)
    
    # 运行测试
    tests = [
        test_provider_detection,
        test_environment_variables,
        test_provider_info,
        test_llm_creation,
        test_k8s_llm_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过!")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置")
        return 1


if __name__ == "__main__":
    sys.exit(main())
