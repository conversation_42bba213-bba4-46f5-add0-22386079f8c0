# K8s MCP Agent 数据真实性铁律

## 🚫 不可违反的核心原则

### 绝对禁止行为

#### ❌ 1. 编造集群数据
- **禁止**: Gemini 2.5 Flash编造任何K8s集群数据
- **禁止**: 基于AI训练知识假设集群状态
- **禁止**: 提供未经MCP工具验证的配置信息
- **禁止**: 使用示例性质的集群信息

#### ❌ 2. 模拟响应
- **禁止**: 返回占位符数据（如 "my-k8s-cluster", "example.com"）
- **禁止**: 基于常见配置进行推测
- **禁止**: 提供"典型"或"通常"的集群配置
- **禁止**: 使用模板化的响应

#### ❌ 3. 知识填补
- **禁止**: 当MCP工具调用失败时用AI知识填补空白
- **禁止**: 基于历史经验推断当前状态
- **禁止**: 提供无法验证的故障排查建议
- **禁止**: 在缺乏真实数据时给出运维建议

## ✅ 严格遵循原则

### 1. 数据溯源要求
```
每个数据点 → 必须追溯到 → 具体的MCP工具调用
```

- **要求**: 所有集群信息必须来自K8s MCP工具的真实返回
- **要求**: 保持完整的调用链路记录
- **要求**: 标明数据的获取时间和来源工具
- **要求**: 提供可验证的操作路径

### 2. 实时验证机制
- **要求**: 所有操作基于当前集群的实时状态
- **要求**: 不使用缓存的过期数据
- **要求**: 定期刷新关键信息
- **要求**: 确保信息的时效性

### 3. 透明度原则
- **要求**: 明确标识数据来源
- **要求**: 显示工具调用的详细过程
- **要求**: 区分真实数据和分析建议
- **要求**: 提供数据获取的时间戳

## 🎯 AI职责边界

### Gemini 2.5 Flash 的允许职责

#### ✅ 1. 理解用户意图
```python
# 正确示例
用户输入: "检查集群中有问题的Pod"
AI理解: 需要调用 LIST_CORE_RESOURCES 获取Pod列表，然后分析状态
```

#### ✅ 2. 选择合适的MCP工具
```python
# 正确示例
需求: 获取集群信息
选择: GET_CLUSTER_INFO 工具
参数: {"cluster": "用户指定的集群名"}
```

#### ✅ 3. 基于真实数据提供分析建议
```python
# 正确示例
真实数据: Pod状态为 "CrashLoopBackOff"
AI分析: "根据Pod状态，建议检查容器日志和资源限制"
```

### Gemini 2.5 Flash 的禁止行为

#### ❌ 1. 不得编造集群数据
```python
# 错误示例
❌ "您的集群有3个节点，运行Kubernetes v1.24"  # 未经工具验证
✅ "正在查询集群信息..." → 调用MCP工具 → 返回真实结果
```

#### ❌ 2. 不得假设集群配置
```python
# 错误示例
❌ "通常集群会配置Ingress Controller"  # 基于假设
✅ "让我检查您的集群是否配置了Ingress Controller" → 调用工具查询
```

#### ❌ 3. 不得提供未验证的信息
```python
# 错误示例
❌ "您可能需要增加内存限制"  # 未基于真实数据
✅ 基于真实的资源使用数据 → "根据当前内存使用率85%，建议考虑增加限制"
```

## 🔍 数据验证机制

### 1. 工具调用验证
```python
def validate_tool_call(tool_result):
    """验证MCP工具调用结果"""
    if not tool_result.success:
        raise MCPToolCallError(f"工具调用失败: {tool_result.error}")
    
    if not tool_result.data:
        raise DataEmptyError("工具返回空数据")
    
    return tool_result
```

### 2. 数据完整性检查
```python
def validate_cluster_data(cluster_info):
    """验证集群数据完整性"""
    required_fields = ['name', 'version', 'status']
    
    for field in required_fields:
        if field not in cluster_info:
            return False
    
    return True
```

### 3. 溯源记录
```python
def add_data_provenance(result, tool_name, timestamp):
    """添加数据溯源信息"""
    result.metadata = {
        "source_tool": tool_name,
        "call_timestamp": timestamp,
        "data_type": "real_cluster_data",
        "verification_status": "verified"
    }
    return result
```

## 🚨 失败处理原则

### 1. 如实报告原则
```python
# 正确的失败处理
try:
    cluster_info = call_mcp_tool("GET_CLUSTER_INFO", params)
except MCPError as e:
    return f"❌ 无法获取集群信息: {e}"
    # 绝不返回: "集群信息如下: [编造的数据]"
```

### 2. 优雅降级
```python
# 部分工具失败时的处理
available_tools = check_available_tools()
if "GET_POD_LOGS" not in available_tools:
    return "⚠️ 日志查询功能当前不可用，可以尝试其他诊断方法"
```

### 3. 错误分类和处理
```python
ERROR_TYPES = {
    "NETWORK_ERROR": "网络连接问题，请检查MCP服务器状态",
    "PERMISSION_ERROR": "权限不足，请检查集群访问权限", 
    "RESOURCE_NOT_FOUND": "指定的资源不存在",
    "MCP_SERVER_ERROR": "MCP服务器内部错误"
}
```

## 📊 质量保证措施

### 1. 自动化验证
- 每次工具调用后自动验证数据完整性
- 检查返回数据的格式和必要字段
- 验证时间戳的合理性

### 2. 人工审核检查点
- 定期审核AI输出的数据来源
- 检查是否存在编造或假设的内容
- 验证分析建议是否基于真实数据

### 3. 用户反馈机制
- 提供数据来源查询功能
- 允许用户验证信息的准确性
- 收集用户对数据真实性的反馈

## 🔧 实施要求

### 1. 代码层面
- 所有数据输出必须包含来源标识
- 实现强制的数据验证流程
- 禁用任何可能产生模拟数据的代码路径

### 2. 系统层面
- MCP连接失败时直接终止程序
- 不提供任何形式的"离线模式"
- 确保系统只在有真实数据时运行

### 3. 监控层面
- 监控数据来源的分布
- 检测异常的数据模式
- 记录所有工具调用的成功率

## 📋 违规检测

### 自动检测规则
```python
VIOLATION_PATTERNS = [
    "my-k8s-cluster",      # 通用示例名称
    "example.com",         # 示例域名
    "通常情况下",           # 基于假设的表述
    "一般来说",             # 通用性描述
    "可能是",               # 推测性语言
    "应该是"                # 未验证的断言
]
```

### 人工审核标准
- 检查每个具体的数据点是否有对应的工具调用
- 验证时间戳的一致性
- 确认分析建议基于真实数据

## 🎯 成功标准

1. **100%数据真实性**: 所有集群数据都来自真实的MCP工具调用
2. **完整溯源**: 每个数据点都能追溯到具体的工具调用
3. **零编造内容**: 绝不出现任何编造或模拟的集群信息
4. **透明操作**: 用户能够清楚了解数据的来源和获取过程

---

**这些原则是不可妥协的系统基石，必须在所有代码、文档和操作中严格执行。**

**版本**: 1.0  
**生效日期**: 2025-06-18  
**适用范围**: 所有K8s MCP Agent相关组件
