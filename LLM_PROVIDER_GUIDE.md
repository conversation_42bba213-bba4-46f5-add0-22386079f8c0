# LLM提供商配置指南

本项目支持多种LLM提供商，可以灵活切换使用OpenRouter或内网部署的Qwen3-32B模型。

## 🚀 快速开始

### 1. 配置环境变量

编辑 `.env` 文件，设置 `LLM_PROVIDER` 来选择提供商：

```bash
# 选择提供商: "openrouter" 或 "qwen3"
LLM_PROVIDER=qwen3
```

### 2. 使用切换工具

```bash
# 切换到OpenRouter
python switch_provider.py openrouter

# 切换到Qwen3内网模型
python switch_provider.py qwen3

# 查看当前配置
python switch_provider.py status

# 验证配置
python switch_provider.py validate
```

### 3. 测试配置

```bash
# 运行配置测试
python test_llm_config.py
```

## 📋 提供商配置详情

### OpenRouter 配置

当 `LLM_PROVIDER=openrouter` 时使用以下配置：

```bash
# OpenRouter API密钥
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here
```

**支持的模型：**
- `openai/gpt-3.5-turbo` - 默认模型，性价比高
- `anthropic/claude-3-5-sonnet` - 生产环境推荐
- `qwen/qwen2.5-coder-32b-instruct` - 开发环境推荐
- `openai/gpt-4-turbo` - 高精度任务

### Qwen3-32B 内网配置

当 `LLM_PROVIDER=qwen3` 时使用以下配置：

```bash
# 本地VLLM部署的API密钥
QWEN3_API_KEY=EMPTY

# 本地VLLM服务器地址
QWEN3_MODEL_SERVER=http://10.49.121.127:8000/v1

# 模型名称 (对应VLLM部署时的served-model-name)
QWEN3_MODEL_NAME=coder

# 是否启用思考模式
QWEN3_ENABLE_THINKING=false
```

## 🔧 代码使用示例

### 基本使用

```python
from llm_config import create_llm, get_current_provider

# 获取当前提供商
provider = get_current_provider()
print(f"当前使用: {provider}")

# 创建默认LLM实例
llm = create_llm()

# 创建特定类型的LLM
llm_prod = create_llm(model_type="production")
llm_dev = create_llm(model_type="development")
```

### Kubernetes专用配置

```python
from llm_config import create_k8s_llm

# 创建生产环境K8s LLM
llm = create_k8s_llm("production")

# 创建开发环境K8s LLM  
llm = create_k8s_llm("development")

# 创建分析任务LLM
llm = create_k8s_llm("analysis")
```

### 显示配置状态

```python
from llm_config import print_provider_status, get_provider_info

# 打印详细状态
print_provider_status()

# 获取配置信息
info = get_provider_info()
print(info)
```

## 🏗️ VLLM部署

内网Qwen3-32B模型使用VLLM部署，部署脚本：

```bash
# 运行部署脚本
./script/deploy-vllm.sh
```

部署命令详情：
```bash
docker run -d \
  --runtime=nvidia \
  --gpus=all \
  --name coder \
  -v /home/<USER>/model/qwen/Qwen3-32B-AWQ:/model/Qwen3-32B-AWQ \
  -p 8000:8000 \
  --cpuset-cpus 0-55 \
  --ulimit memlock=-1 \
  --ulimit stack=67108864 \
  --restart always \
  --ipc=host \
  vllm/vllm-openai:v0.8.5 \
  --model /model/Qwen3-32B-AWQ \
  --served-model-name coder \
  --tensor-parallel-size 4 \
  --dtype half \
  --quantization awq \
  --max-model-len 32768 \
  --max-num-batched-tokens 4096 \
  --gpu-memory-utilization 0.93 \
  --block-size 32 \
  --enable-chunked-prefill \
  --swap-space 16 \
  --tokenizer-pool-size 56 \
  --disable-custom-all-reduce
```

## 🎯 模型类型说明

| 模型类型 | 用途 | 温度 | 特点 |
|---------|------|------|------|
| `default` | 通用任务 | 0.0 | 平衡性能和成本 |
| `production` | 生产环境 | 0.0 | 最高精度，零随机性 |
| `development` | 开发测试 | 0.1 | 略微灵活，成本优化 |
| `analysis` | 分析任务 | 0.0 | 大输出，适合日志分析 |

## 🔒 安全配置

所有LLM配置都包含安全停止序列，防止执行危险命令：

```python
SAFETY_STOP_SEQUENCES = [
    "```bash",
    "```sh", 
    "```shell",
    "rm -rf",
    "kubectl delete",
    "docker rmi",
    "sudo rm"
]
```

## 🚨 故障排除

### 常见问题

1. **无法连接到Qwen3服务器**
   ```bash
   # 检查服务器状态
   curl http://10.49.121.127:8000/v1/models
   
   # 检查Docker容器
   docker ps | grep coder
   ```

2. **OpenRouter API密钥错误**
   ```bash
   # 验证API密钥
   python switch_provider.py validate
   ```

3. **模型响应异常**
   ```bash
   # 运行完整测试
   python test_llm_config.py
   ```

### 调试步骤

1. 检查环境变量配置
2. 验证网络连接
3. 查看服务日志
4. 运行测试脚本

## 📚 相关文件

- `llm_config.py` - 核心配置模块
- `switch_provider.py` - 提供商切换工具
- `test_llm_config.py` - 配置测试脚本
- `main.py` - 主程序示例
- `k8s_config.py` - Kubernetes专用配置（兼容模式）

## 🔄 迁移指南

从旧配置迁移到新配置系统：

### 旧代码
```python
from langchain_openai import ChatOpenAI
import os

llm = ChatOpenAI(
    model="openai/gpt-3.5-turbo",
    api_key=os.getenv("OPENROUTER_API_KEY"),
    base_url="https://openrouter.ai/api/v1",
    temperature=0.0
)
```

### 新代码
```python
from llm_config import create_llm

# 自动根据配置选择提供商
llm = create_llm(model_type="default")
```

这样可以无缝切换不同的提供商，无需修改代码！
