#!/usr/bin/env python3
"""
Gemini 2.5 Flash MCP测试
验证完整的工具调用功能
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from mcp_use import MCPAgent, MCPClient
from src.llm_config import create_mcp_compatible_llm


async def test_cluster_info():
    """测试获取具体集群信息"""
    print("🔍 测试获取具体集群信息")
    print("=" * 50)
    
    try:
        # 使用内网MCP服务器配置
        config = {
            "mcpServers": {
                "k8s": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }
        
        # 创建MCP客户端
        print("🔗 创建MCP客户端...")
        client = MCPClient.from_dict(config)
        
        # 创建Gemini 2.5 Flash最大能力LLM
        print("🔧 创建Gemini 2.5 Flash最大能力LLM...")
        llm = create_mcp_compatible_llm()
        
        print(f"✅ LLM创建成功: {llm.model_name}")
        
        # 创建Agent
        print("🤖 创建MCP Agent...")
        agent = MCPAgent(llm=llm, client=client, max_steps=5)
        
        # 测试获取具体集群信息
        print("💬 测试获取gfxc-dev1集群信息...")
        result = await agent.run(
            "请获取gfxc-dev1集群的详细信息",
            max_steps=3,
        )
        
        print(f"📋 集群信息结果:")
        print("-" * 40)
        print(result)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ 集群信息测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_pod_operations():
    """测试Pod相关操作"""
    print("\n🐳 测试Pod相关操作")
    print("=" * 50)
    
    try:
        config = {
            "mcpServers": {
                "k8s": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }
        
        client = MCPClient.from_dict(config)
        llm = create_mcp_compatible_llm()
        agent = MCPAgent(llm=llm, client=client, max_steps=5)
        
        # 测试列出Pod
        print("💬 测试列出gfxc-dev1集群的Pod...")
        result = await agent.run(
            "请列出gfxc-dev1集群default命名空间中的所有Pod",
            max_steps=3,
        )
        
        print(f"📋 Pod列表结果:")
        print("-" * 40)
        print(result)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ Pod操作测试失败: {e}")
        return False


async def test_resource_usage():
    """测试资源使用情况查询"""
    print("\n📊 测试资源使用情况查询")
    print("=" * 50)
    
    try:
        config = {
            "mcpServers": {
                "k8s": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }
        
        client = MCPClient.from_dict(config)
        llm = create_mcp_compatible_llm()
        agent = MCPAgent(llm=llm, client=client, max_steps=5)
        
        # 测试资源使用情况
        print("💬 测试获取gfxc-dev1集群资源使用情况...")
        result = await agent.run(
            "请获取gfxc-dev1集群的资源使用情况",
            max_steps=3,
        )
        
        print(f"📋 资源使用结果:")
        print("-" * 40)
        print(result)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ 资源使用测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 Gemini 2.5 Flash MCP完整功能测试")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    # 显示当前配置
    print("🤖 当前LLM模型: Gemini 2.5 Flash")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("集群信息查询", test_cluster_info),
        ("Pod操作测试", test_pod_operations),
        ("资源使用查询", test_resource_usage),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行 {test_name}...")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! Gemini 2.5 Flash完美解决了工具调用问题!")
        print("\n💡 现在您可以:")
        print("  - 使用完整的MCP工具调用功能")
        print("  - 进行复杂的Kubernetes运维操作")
        print("  - 享受大上下文和推理能力")
        print("  - 不再担心strict模式问题")
    elif passed > 0:
        print("🎯 部分测试通过，基础功能正常!")
    else:
        print("⚠️ 所有测试失败，需要进一步调试")


if __name__ == "__main__":
    asyncio.run(main())
