"""
LLM Provider Configuration Module
支持多种LLM提供商的统一配置管理
"""

import os
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI


class LLMProviderConfig:
    """
    LLM提供商配置管理器
    支持OpenRouter和内网Qwen3模型的统一配置
    """
    
    # 支持的提供商
    SUPPORTED_PROVIDERS = ["openrouter", "qwen3"]
    
    # 安全停止序列
    SAFETY_STOP_SEQUENCES = [
        "```bash",
        "```sh", 
        "```shell",
        "rm -rf",
        "kubectl delete",
        "docker rmi",
        "sudo rm"
    ]
    
    def __init__(self):
        """初始化配置管理器"""
        load_dotenv()
        self.provider = os.getenv("LLM_PROVIDER", "openrouter").lower()
        
        if self.provider not in self.SUPPORTED_PROVIDERS:
            raise ValueError(f"不支持的LLM提供商: {self.provider}. 支持的提供商: {self.SUPPORTED_PROVIDERS}")
    
    def get_provider(self) -> str:
        """获取当前配置的提供商"""
        return self.provider
    
    def create_llm(self, 
                   model_type: str = "default",
                   temperature: float = 0.0,
                   max_tokens: int = 4096,
                   **kwargs) -> ChatOpenAI:
        """
        根据配置的提供商创建LLM实例
        
        Args:
            model_type: 模型类型 ("default", "production", "development", "analysis")
            temperature: 温度参数
            max_tokens: 最大输出token数
            **kwargs: 其他参数
            
        Returns:
            配置好的ChatOpenAI实例
        """
        if self.provider == "openrouter":
            return self._create_openrouter_llm(model_type, temperature, max_tokens, **kwargs)
        elif self.provider == "qwen3":
            return self._create_qwen3_llm(model_type, temperature, max_tokens, **kwargs)
        else:
            raise ValueError(f"不支持的提供商: {self.provider}")
    
    def _create_openrouter_llm(self, 
                              model_type: str,
                              temperature: float,
                              max_tokens: int,
                              **kwargs) -> ChatOpenAI:
        """创建OpenRouter LLM实例"""
        
        # 根据模型类型选择模型
        model_map = {
            "default": "openai/gpt-3.5-turbo",
            "production": "anthropic/claude-3-5-sonnet",
            "development": "qwen/qwen2.5-coder-32b-instruct",
            "analysis": "anthropic/claude-3-5-sonnet",
            "k8s": "anthropic/claude-3-5-sonnet"
        }
        
        model = model_map.get(model_type, "openai/gpt-3.5-turbo")
        
        return ChatOpenAI(
            model=model,
            api_key=os.getenv("OPENROUTER_API_KEY"),
            base_url="https://openrouter.ai/api/v1",
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=kwargs.get("top_p", 0.1),
            frequency_penalty=kwargs.get("frequency_penalty", 0.0),
            presence_penalty=kwargs.get("presence_penalty", 0.0),
            max_retries=kwargs.get("max_retries", 5),
            request_timeout=kwargs.get("request_timeout", 120),
            streaming=kwargs.get("streaming", False),
            stop=kwargs.get("stop", self.SAFETY_STOP_SEQUENCES),
            model_kwargs=kwargs.get("model_kwargs", {"seed": 42})
        )
    
    def _create_qwen3_llm(self, 
                         model_type: str,
                         temperature: float,
                         max_tokens: int,
                         **kwargs) -> ChatOpenAI:
        """创建内网Qwen3 LLM实例"""
        
        # Qwen3模型配置
        model_name = os.getenv("QWEN3_MODEL_NAME", "coder")
        
        # 根据模型类型调整参数
        if model_type == "production":
            temperature = 0.0
            top_p = 0.05
        elif model_type == "development":
            temperature = max(temperature, 0.1)
            top_p = 0.2
        elif model_type == "analysis":
            temperature = 0.0
            top_p = 0.1
            max_tokens = min(max_tokens * 2, 8192)  # 分析任务需要更多输出
        else:
            top_p = kwargs.get("top_p", 0.1)
        
        # 检查是否启用思考模式
        enable_thinking = os.getenv("QWEN3_ENABLE_THINKING", "false").lower() == "true"
        
        model_kwargs = kwargs.get("model_kwargs", {})
        if enable_thinking:
            model_kwargs.update({
                "thinking_mode": True,
                "max_thinking_tokens": 2048
            })
        
        # 检查VLLM服务器是否支持工具调用
        server_url = os.getenv("QWEN3_MODEL_SERVER")
        supports_tools = self._check_tool_support(server_url)

        # 根据服务器能力调整配置
        if not supports_tools:
            # 如果服务器不支持工具调用，禁用相关功能
            model_kwargs.update({
                "tool_choice": "none",  # 禁用工具选择
            })
            print(f"⚠️  Qwen3服务器不支持工具调用，已禁用工具功能")

        return ChatOpenAI(
            model=model_name,
            api_key=os.getenv("QWEN3_API_KEY", "EMPTY"),
            base_url=server_url,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=top_p,
            frequency_penalty=kwargs.get("frequency_penalty", 0.0),
            presence_penalty=kwargs.get("presence_penalty", 0.0),
            max_retries=kwargs.get("max_retries", 3),
            request_timeout=kwargs.get("request_timeout", 120),
            streaming=kwargs.get("streaming", False),
            stop=kwargs.get("stop", self.SAFETY_STOP_SEQUENCES),
            model_kwargs=model_kwargs
        )
    
    def _check_tool_support(self, server_url: str) -> bool:
        """检查VLLM服务器是否支持工具调用"""
        if not server_url:
            return False

        try:
            import requests
            # 检查服务器的模型信息
            response = requests.get(f"{server_url}/v1/models", timeout=5)
            if response.status_code == 200:
                # 这里可以添加更详细的检查逻辑
                # 目前简单返回False，因为我们知道当前服务器不支持
                return False
        except Exception:
            pass

        return False

    def get_provider_info(self) -> Dict[str, Any]:
        """获取当前提供商的信息"""
        if self.provider == "openrouter":
            return {
                "provider": "OpenRouter",
                "api_key": os.getenv("OPENROUTER_API_KEY", "")[:10] + "..." if os.getenv("OPENROUTER_API_KEY") else "未设置",
                "base_url": "https://openrouter.ai/api/v1",
                "models": ["gpt-3.5-turbo", "claude-3-5-sonnet", "qwen2.5-coder-32b"]
            }
        elif self.provider == "qwen3":
            return {
                "provider": "Qwen3-32B (内网)",
                "api_key": os.getenv("QWEN3_API_KEY", "EMPTY"),
                "base_url": os.getenv("QWEN3_MODEL_SERVER"),
                "model_name": os.getenv("QWEN3_MODEL_NAME", "coder"),
                "thinking_mode": os.getenv("QWEN3_ENABLE_THINKING", "false")
            }


# 全局配置实例
llm_config = LLMProviderConfig()


def create_llm(model_type: str = "default", **kwargs) -> ChatOpenAI:
    """
    便捷函数：创建LLM实例
    
    Args:
        model_type: 模型类型
        **kwargs: 其他参数
        
    Returns:
        配置好的ChatOpenAI实例
    """
    return llm_config.create_llm(model_type=model_type, **kwargs)


def get_current_provider() -> str:
    """获取当前配置的提供商"""
    return llm_config.get_provider()


def get_provider_info() -> Dict[str, Any]:
    """获取当前提供商信息"""
    return llm_config.get_provider_info()


def print_provider_status():
    """打印当前提供商状态"""
    info = get_provider_info()
    print(f"🤖 当前LLM提供商: {info['provider']}")
    print(f"🔗 服务地址: {info.get('base_url', 'N/A')}")
    
    if llm_config.provider == "openrouter":
        print(f"🔑 API密钥: {info['api_key']}")
        print(f"📋 可用模型: {', '.join(info['models'])}")
    elif llm_config.provider == "qwen3":
        print(f"🔑 API密钥: {info['api_key']}")
        print(f"🏷️  模型名称: {info['model_name']}")
        print(f"🧠 思考模式: {info['thinking_mode']}")


# Kubernetes专用配置
def create_mcp_compatible_llm(environment: str = "production") -> ChatOpenAI:
    """
    创建MCP兼容的LLM实例
    专门处理工具调用兼容性问题

    Args:
        environment: 环境类型 (production, development, analysis)

    Returns:
        MCP兼容的ChatOpenAI实例
    """
    if llm_config.provider == "qwen3":
        # Qwen3需要特殊处理工具调用
        mcp_configs = {
            "production": {
                "model_type": "production",
                "temperature": 0.0,
                "max_tokens": 4096,
                "top_p": 0.05,
                "max_retries": 5,
                "request_timeout": 120,
                "model_kwargs": {
                    "tool_choice": "none",  # 禁用自动工具选择
                    "parallel_tool_calls": False,  # 禁用并行工具调用
                }
            },
            "development": {
                "model_type": "development",
                "temperature": 0.1,
                "max_tokens": 3072,
                "top_p": 0.2,
                "max_retries": 3,
                "request_timeout": 90,
                "model_kwargs": {
                    "tool_choice": "none",
                    "parallel_tool_calls": False,
                }
            },
            "analysis": {
                "model_type": "analysis",
                "temperature": 0.0,
                "max_tokens": 8192,
                "top_p": 0.1,
                "max_retries": 5,
                "request_timeout": 180,
                "model_kwargs": {
                    "tool_choice": "none",
                    "parallel_tool_calls": False,
                }
            }
        }

        config = mcp_configs.get(environment, mcp_configs["production"])
        return llm_config.create_llm(**config)

    else:
        # OpenRouter支持完整的工具调用
        return create_k8s_llm(environment)


def create_k8s_llm(environment: str = "production") -> ChatOpenAI:
    """
    创建Kubernetes专用的LLM实例
    
    Args:
        environment: 环境类型 (production, development, analysis)
        
    Returns:
        针对K8s优化的ChatOpenAI实例
    """
    k8s_configs = {
        "production": {
            "model_type": "production",
            "temperature": 0.0,
            "max_tokens": 4096,
            "top_p": 0.05,
            "max_retries": 5,
            "request_timeout": 120
        },
        "development": {
            "model_type": "development", 
            "temperature": 0.1,
            "max_tokens": 3072,
            "top_p": 0.2,
            "max_retries": 3,
            "request_timeout": 90
        },
        "analysis": {
            "model_type": "analysis",
            "temperature": 0.0,
            "max_tokens": 8192,
            "top_p": 0.1,
            "max_retries": 5,
            "request_timeout": 180
        }
    }
    
    config = k8s_configs.get(environment, k8s_configs["production"])
    return llm_config.create_llm(**config)
