"""
K8s MCP Agent LLM配置模块
专用于 Gemini 2.5 Flash 模型的最大能力配置
单一、统一的配置，始终使用最大上下文和输出能力
"""

import os
from typing import Dict, Any
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI

# 加载环境变量
load_dotenv()


class GeminiMaxConfig:
    """
    Gemini 2.5 Flash 最大能力配置
    始终使用最大上下文和输出能力的单一配置
    """

    # 固定使用的模型
    MODEL_NAME = "google/gemini-2.5-flash"

    # 最大能力配置
    MAX_INPUT_CONTEXT = 1048576      # 1,048,576 tokens 输入上下文
    MAX_OUTPUT_TOKENS = 32768        # 32,768 tokens 最大输出
    MAX_TIMEOUT = 600                # 600秒超时，适合大上下文处理

    # 安全停止序列
    SAFETY_STOP_SEQUENCES = [
        "```bash",
        "```sh",
        "```shell",
        "rm -rf",
        "kubectl delete",
        "docker rmi",
        "sudo rm"
    ]

    def __init__(self):
        """初始化Gemini最大能力配置管理器"""
        # 验证必要的环境变量
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY 环境变量未设置")

    def get_model_name(self) -> str:
        """获取模型名称"""
        return self.MODEL_NAME

    def get_max_capabilities(self) -> Dict[str, Any]:
        """获取最大能力信息"""
        return {
            "model": self.MODEL_NAME,
            "input_context": self.MAX_INPUT_CONTEXT,
            "output_tokens": self.MAX_OUTPUT_TOKENS,
            "timeout": self.MAX_TIMEOUT,
            "description": "始终使用Gemini 2.5 Flash的最大能力配置"
        }
    
    def create_llm(self, **kwargs) -> ChatOpenAI:
        """
        创建Gemini 2.5 Flash最大能力LLM实例
        始终使用最大上下文和输出能力，无环境区分

        Args:
            **kwargs: 可选的覆盖参数（通常不需要）

        Returns:
            配置为最大能力的Gemini 2.5 Flash ChatOpenAI实例
        """
        return ChatOpenAI(
            model=self.MODEL_NAME,
            api_key=os.getenv("OPENROUTER_API_KEY"),
            base_url="https://openrouter.ai/api/v1",

            # 最大能力配置
            max_tokens=kwargs.get("max_tokens", self.MAX_OUTPUT_TOKENS),
            temperature=kwargs.get("temperature", 0.0),  # 确定性输出
            top_p=kwargs.get("top_p", 0.05),  # 高精度token选择

            # 稳定性配置
            frequency_penalty=kwargs.get("frequency_penalty", 0.0),
            presence_penalty=kwargs.get("presence_penalty", 0.0),
            streaming=kwargs.get("streaming", False),

            # 可靠性配置
            max_retries=kwargs.get("max_retries", 5),
            request_timeout=kwargs.get("request_timeout", self.MAX_TIMEOUT),

            # 安全配置
            stop=kwargs.get("stop", self.SAFETY_STOP_SEQUENCES),
            model_kwargs=kwargs.get("model_kwargs", {"seed": 42})
        )
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "provider": "OpenRouter",
            "model": self.MODEL_NAME,
            "api_key": os.getenv("OPENROUTER_API_KEY", "")[:10] + "..." if os.getenv("OPENROUTER_API_KEY") else "未设置",
            "base_url": "https://openrouter.ai/api/v1",
            "input_context": f"{self.MAX_INPUT_CONTEXT:,} tokens",
            "output_tokens": f"{self.MAX_OUTPUT_TOKENS:,} tokens",
            "timeout": f"{self.MAX_TIMEOUT}s",
            "features": ["工具调用", "最大上下文", "推理模式", "数学/编程", "K8s运维"],
            "configuration": "最大能力配置 - 无环境区分"
        }


# 全局配置实例
gemini_config = GeminiMaxConfig()


def create_llm(**kwargs) -> ChatOpenAI:
    """
    便捷函数：创建Gemini 2.5 Flash最大能力LLM实例
    始终使用最大上下文和输出能力，无环境区分

    Args:
        **kwargs: 可选的覆盖参数（通常不需要）

    Returns:
        配置为最大能力的Gemini 2.5 Flash ChatOpenAI实例
    """
    return gemini_config.create_llm(**kwargs)


def get_model_info() -> Dict[str, Any]:
    """获取模型信息"""
    return gemini_config.get_model_info()


def print_model_status():
    """打印当前模型状态"""
    info = get_model_info()
    print(f"🤖 当前LLM模型: {info['model']}")
    print(f"🔗 服务地址: {info['base_url']}")
    print(f"🔑 API密钥: {info['api_key']}")
    print(f"📏 输入上下文: {info['input_context']}")
    print(f"📤 输出能力: {info['output_tokens']}")
    print(f"⏱️  超时设置: {info['timeout']}")
    print(f"🛠️  功能特性: {', '.join(info['features'])}")
    print(f"⚙️  配置模式: {info['configuration']}")


def create_mcp_compatible_llm() -> ChatOpenAI:
    """
    创建MCP兼容的Gemini 2.5 Flash LLM实例
    专门用于K8s MCP Agent，始终使用最大能力配置

    Returns:
        MCP兼容的Gemini 2.5 Flash最大能力ChatOpenAI实例
    """
    print("🚀 创建Gemini 2.5 Flash最大能力LLM实例")
    return create_llm()


def create_k8s_llm() -> ChatOpenAI:
    """
    创建Kubernetes专用的Gemini 2.5 Flash LLM实例
    始终使用最大能力配置

    Returns:
        针对K8s优化的Gemini 2.5 Flash最大能力ChatOpenAI实例
    """
    return create_llm()


def create_large_context_llm(max_tokens: int = None) -> ChatOpenAI:
    """
    创建大上下文专用的Gemini 2.5 Flash LLM实例
    充分利用1,048,576 tokens的输入上下文能力

    Args:
        max_tokens: 最大输出token数，默认使用最大值32768

    Returns:
        大上下文优化的Gemini 2.5 Flash ChatOpenAI实例

    Use Cases:
        - 分析大量K8s日志
        - 处理复杂的YAML配置
        - 多集群状态对比
        - 详细的故障排查报告
    """
    if max_tokens is None:
        max_tokens = gemini_config.MAX_OUTPUT_TOKENS

    return create_llm(max_tokens=max_tokens)


def get_context_info() -> Dict[str, Any]:
    """
    获取Gemini 2.5 Flash上下文能力信息

    Returns:
        上下文能力详细信息
    """
    return {
        "input_context_length": gemini_config.MAX_INPUT_CONTEXT,
        "input_context_description": "支持处理超大文档、日志文件、配置集合",
        "output_tokens": gemini_config.MAX_OUTPUT_TOKENS,
        "timeout": gemini_config.MAX_TIMEOUT,
        "configuration": "单一最大能力配置",
        "use_cases": [
            "分析完整的K8s集群状态",
            "处理大量Pod日志",
            "比较多个YAML配置文件",
            "生成详细的故障排查报告",
            "分析复杂的网络配置",
            "处理大规模部署清单"
        ]
    }


# 向后兼容的函数
def get_current_provider() -> str:
    """获取当前提供商（向后兼容）"""
    return "openrouter"


def get_provider_info() -> Dict[str, Any]:
    """获取提供商信息（向后兼容）"""
    info = get_model_info()
    return {
        "provider": "OpenRouter (Gemini 2.5 Flash 最大能力)",
        "api_key": info["api_key"],
        "base_url": info["base_url"],
        "primary_model": info["model"],
        "input_context": info["input_context"],
        "output_tokens": info["output_tokens"],
        "timeout": info["timeout"],
        "features": info["features"],
        "configuration": info["configuration"]
    }


def print_provider_status():
    """打印提供商状态（向后兼容）"""
    print_model_status()


# 验证配置
def validate_config():
    """验证配置是否正确"""
    try:
        # 尝试创建一个LLM实例
        llm = create_llm()
        print("✅ Gemini 2.5 Flash 最大能力配置验证成功")
        return True
    except Exception as e:
        print(f"❌ Gemini 2.5 Flash 配置验证失败: {e}")
        return False


if __name__ == "__main__":
    # 配置验证
    print("🧪 Gemini 2.5 Flash 最大能力配置验证")
    print("=" * 60)

    if validate_config():
        print_model_status()
        print("\n💡 配置特点:")
        print("   - 始终使用最大输入上下文 (1,048,576 tokens)")
        print("   - 始终使用最大输出能力 (32,768 tokens)")
        print("   - 始终使用最长超时时间 (600秒)")
        print("   - 无环境区分，统一最大能力配置")
    else:
        print("请检查 OPENROUTER_API_KEY 环境变量设置")
