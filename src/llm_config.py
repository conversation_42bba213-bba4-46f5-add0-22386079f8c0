"""
K8s MCP Agent LLM配置模块
专用于 Gemini 2.5 Flash 模型配置和管理
"""

import os
from typing import Dict, Any
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI

# 加载环境变量
load_dotenv()


class GeminiLLMConfig:
    """
    Gemini 2.5 Flash 专用配置管理器
    简化的单一模型配置，专注于K8s MCP Agent
    """
    
    # 固定使用的模型
    MODEL_NAME = "google/gemini-2.5-flash"
    
    # 安全停止序列
    SAFETY_STOP_SEQUENCES = [
        "```bash",
        "```sh", 
        "```shell",
        "rm -rf",
        "kubectl delete",
        "docker rmi",
        "sudo rm"
    ]
    
    def __init__(self):
        """初始化Gemini配置管理器"""
        # 验证必要的环境变量
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY 环境变量未设置")
    
    def get_model_name(self) -> str:
        """获取模型名称"""
        return self.MODEL_NAME
    
    def create_llm(self, 
                   environment: str = "production",
                   temperature: float = 0.0,
                   max_tokens: int = 4096,
                   **kwargs) -> ChatOpenAI:
        """
        创建Gemini 2.5 Flash LLM实例
        
        Args:
            environment: 环境类型 ("production", "development", "analysis")
            temperature: 温度参数
            max_tokens: 最大输出token数
            **kwargs: 其他参数
            
        Returns:
            配置好的Gemini 2.5 Flash ChatOpenAI实例
        """
        # 根据环境调整参数
        config = self._get_environment_config(environment)
        
        # 合并用户参数和环境配置
        final_temperature = kwargs.get('temperature', config.get('temperature', temperature))
        final_max_tokens = kwargs.get('max_tokens', config.get('max_tokens', max_tokens))
        
        return ChatOpenAI(
            model=self.MODEL_NAME,
            api_key=os.getenv("OPENROUTER_API_KEY"),
            base_url="https://openrouter.ai/api/v1",
            max_tokens=final_max_tokens,
            temperature=final_temperature,
            top_p=kwargs.get("top_p", config.get("top_p", 0.1)),
            frequency_penalty=kwargs.get("frequency_penalty", 0.0),
            presence_penalty=kwargs.get("presence_penalty", 0.0),
            max_retries=kwargs.get("max_retries", config.get("max_retries", 5)),
            request_timeout=kwargs.get("request_timeout", config.get("request_timeout", 120)),
            streaming=kwargs.get("streaming", False),
            stop=kwargs.get("stop", self.SAFETY_STOP_SEQUENCES),
            model_kwargs=kwargs.get("model_kwargs", {"seed": 42})
        )
    
    def _get_environment_config(self, environment: str) -> Dict[str, Any]:
        """获取环境特定的配置"""
        configs = {
            "production": {
                "temperature": 0.0,
                "max_tokens": 4096,
                "top_p": 0.05,
                "max_retries": 5,
                "request_timeout": 120
            },
            "development": {
                "temperature": 0.1,
                "max_tokens": 3072,
                "top_p": 0.2,
                "max_retries": 3,
                "request_timeout": 90
            },
            "analysis": {
                "temperature": 0.0,
                "max_tokens": 8192,
                "top_p": 0.1,
                "max_retries": 5,
                "request_timeout": 180
            }
        }
        return configs.get(environment, configs["production"])
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "provider": "OpenRouter",
            "model": self.MODEL_NAME,
            "api_key": os.getenv("OPENROUTER_API_KEY", "")[:10] + "..." if os.getenv("OPENROUTER_API_KEY") else "未设置",
            "base_url": "https://openrouter.ai/api/v1",
            "context_length": "1,048,576 tokens",
            "features": ["工具调用", "大上下文", "推理模式", "数学/编程", "K8s运维"]
        }


# 全局配置实例
gemini_config = GeminiLLMConfig()


def create_llm(environment: str = "production", **kwargs) -> ChatOpenAI:
    """
    便捷函数：创建Gemini 2.5 Flash LLM实例
    
    Args:
        environment: 环境类型 ("production", "development", "analysis")
        **kwargs: 其他参数
        
    Returns:
        配置好的Gemini 2.5 Flash ChatOpenAI实例
    """
    return gemini_config.create_llm(environment=environment, **kwargs)


def get_model_info() -> Dict[str, Any]:
    """获取模型信息"""
    return gemini_config.get_model_info()


def print_model_status():
    """打印当前模型状态"""
    info = get_model_info()
    print(f"🤖 当前LLM模型: {info['model']}")
    print(f"🔗 服务地址: {info['base_url']}")
    print(f"🔑 API密钥: {info['api_key']}")
    print(f"📏 上下文长度: {info['context_length']}")
    print(f"🛠️  功能特性: {', '.join(info['features'])}")


def create_mcp_compatible_llm(environment: str = "production") -> ChatOpenAI:
    """
    创建MCP兼容的Gemini 2.5 Flash LLM实例
    专门用于K8s MCP Agent
    
    Args:
        environment: 环境类型 (production, development, analysis)
        
    Returns:
        MCP兼容的Gemini 2.5 Flash ChatOpenAI实例
    """
    print(f"🚀 创建Gemini 2.5 Flash LLM实例 (环境: {environment})")
    return create_llm(environment=environment)


def create_k8s_llm(environment: str = "production") -> ChatOpenAI:
    """
    创建Kubernetes专用的Gemini 2.5 Flash LLM实例
    
    Args:
        environment: 环境类型 (production, development, analysis)
        
    Returns:
        针对K8s优化的Gemini 2.5 Flash ChatOpenAI实例
    """
    return create_llm(environment=environment)


# 向后兼容的函数
def get_current_provider() -> str:
    """获取当前提供商（向后兼容）"""
    return "openrouter"


def get_provider_info() -> Dict[str, Any]:
    """获取提供商信息（向后兼容）"""
    info = get_model_info()
    return {
        "provider": "OpenRouter (Gemini 2.5 Flash)",
        "api_key": info["api_key"],
        "base_url": info["base_url"],
        "primary_model": info["model"],
        "context_length": info["context_length"],
        "features": info["features"]
    }


def print_provider_status():
    """打印提供商状态（向后兼容）"""
    print_model_status()


# 验证配置
def validate_config():
    """验证配置是否正确"""
    try:
        # 尝试创建一个LLM实例
        llm = create_llm("production")
        print("✅ Gemini 2.5 Flash 配置验证成功")
        return True
    except Exception as e:
        print(f"❌ Gemini 2.5 Flash 配置验证失败: {e}")
        return False


if __name__ == "__main__":
    # 配置验证
    print("🧪 Gemini 2.5 Flash 配置验证")
    print("=" * 50)
    
    if validate_config():
        print_model_status()
    else:
        print("请检查 OPENROUTER_API_KEY 环境变量设置")
