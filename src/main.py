import asyncio
import os
from dotenv import load_dotenv
from mcp_use import MCPAgent, MCPClient
from .llm_config import create_mcp_compatible_llm, print_provider_status, get_current_provider


async def main():
    """Run the example using a configuration file."""
    # Load environment variables
    load_dotenv()

    # 打印当前LLM提供商状态
    print("=" * 60)
    print("🚀 MCP Agent 启动")
    print("=" * 60)
    print_provider_status()
    print("=" * 60)

    # 根据配置创建LLM实例
    # 自动根据 .env 中的 LLM_PROVIDER 选择提供商
    print(f"🔧 正在初始化 {get_current_provider().upper()} LLM...")
    llm = create_mcp_compatible_llm("production")  # 使用MCP兼容的配置

    print(f"✅ LLM初始化完成")
    print(f"   模型: {llm.model_name}")
    print(f"   温度: {llm.temperature}")
    print(f"   最大Token: {llm.max_tokens}")
    print(f"   服务地址: {llm.openai_api_base}")
    print("=" * 60)

    # 尝试连接MCP服务器
    try:
        config = {
            "mcpServers": {
                "dev-k8s-mcp": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }

        # Create MCPClient from config file
        client = MCPClient.from_dict(config)

        # Create agent with the client
        agent = MCPAgent(llm=llm, client=client, max_steps=30)

        # Run the query
        print("🔍 开始执行MCP任务...")
        result = await agent.run(
            "get the k8s cluster info",
            max_steps=30,
        )
        print(f"\n✅ MCP任务完成!")
        print(f"📋 结果: {result}")

    except Exception as e:
        print(f"❌ MCP连接失败: {e}")
        print("💡 提示: MCP服务器可能不可用，但LLM配置系统工作正常")
        print("🔧 建议使用独立模式: uv run python standalone.py")

        # 运行简单的LLM测试
        print("\n🧪 运行简单LLM测试...")
        try:
            response = await llm.ainvoke("你好，请简单介绍一下你自己")
            print(f"🤖 LLM回复: {response.content[:100]}...")
            print("✅ LLM功能正常!")
        except Exception as llm_error:
            print(f"❌ LLM测试失败: {llm_error}")

if __name__ == "__main__":
    # Run the appropriate example
    asyncio.run(main())
