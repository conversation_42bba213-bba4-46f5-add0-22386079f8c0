"""
严格MCP配置
确保只使用真实的MCP工具调用，不允许模拟数据
"""

import os
from typing import Dict, Any
from langchain_openai import ChatOpenAI


def create_strict_mcp_llm() -> ChatOpenAI:
    """
    创建严格模式的MCP LLM
    - 禁用工具调用模拟
    - 强制使用真实MCP工具
    - 只负责格式化真实返回数据
    """
    
    # 严格的系统提示词
    strict_system_prompt = """
你是一个严格的Kubernetes助手。重要规则：

1. 只能使用提供的MCP工具获取真实数据
2. 绝对不允许编造、模拟或假设任何数据
3. 如果工具调用失败，必须如实报告
4. 只对真实返回的数据进行格式化和排版
5. 不要添加任何未经工具确认的信息

工具调用格式：
- 必须严格按照 Action/Action Input/Observation 格式
- Action Input 必须是有效的JSON格式
- 等待真实的 Observation 结果再继续

如果无法获取真实数据，请明确说明原因，不要提供虚假信息。
"""
    
    # 创建严格配置的LLM
    llm = ChatOpenAI(
        model=os.getenv("QWEN3_MODEL_NAME", "coder"),
        api_key=os.getenv("QWEN3_API_KEY", "EMPTY"),
        base_url=os.getenv("QWEN3_MODEL_SERVER"),
        max_tokens=2048,  # 减少输出长度，避免发散
        temperature=0.0,  # 完全确定性
        top_p=0.05,  # 极低的随机性
        frequency_penalty=0.5,  # 避免重复
        presence_penalty=0.3,  # 鼓励简洁
        max_retries=3,
        request_timeout=60,
        streaming=False,
        model_kwargs={
            "tool_choice": "none",  # 禁用自动工具选择，让Agent控制
            "parallel_tool_calls": False,
            "response_format": {"type": "text"},
        }
    )
    
    return llm


def create_debug_mcp_llm() -> ChatOpenAI:
    """
    创建调试模式的MCP LLM
    显示详细的工具调用过程
    """
    
    debug_system_prompt = """
你是一个调试模式的Kubernetes助手。

调试要求：
1. 详细记录每个工具调用的过程
2. 显示真实的工具输入和输出
3. 如实报告任何错误或异常
4. 不要编造任何数据

格式要求：
- 明确标识每个步骤
- 显示工具调用的详细信息
- 区分真实数据和格式化输出
"""
    
    llm = ChatOpenAI(
        model=os.getenv("QWEN3_MODEL_NAME", "coder"),
        api_key=os.getenv("QWEN3_API_KEY", "EMPTY"),
        base_url=os.getenv("QWEN3_MODEL_SERVER"),
        max_tokens=1024,
        temperature=0.0,
        top_p=0.1,
        frequency_penalty=0.0,
        presence_penalty=0.0,
        max_retries=3,
        request_timeout=60,
        streaming=False,
        model_kwargs={
            "tool_choice": "none",
            "parallel_tool_calls": False,
        }
    )
    
    return llm


def validate_mcp_response(response: str) -> Dict[str, Any]:
    """
    验证MCP响应的真实性
    
    Args:
        response: MCP Agent的响应
        
    Returns:
        验证结果
    """
    validation_result = {
        "is_real_data": False,
        "has_tool_calls": False,
        "has_observations": False,
        "potential_simulation": False,
        "warnings": []
    }
    
    # 检查是否包含工具调用
    if "Action:" in response and "Action Input:" in response:
        validation_result["has_tool_calls"] = True
    
    # 检查是否包含观察结果
    if "Observation:" in response:
        validation_result["has_observations"] = True
    
    # 检查可能的模拟数据标识
    simulation_indicators = [
        "my-k8s-cluster",  # 通用集群名
        "example.com",
        "localhost",
        "test-",
        "demo-",
        "sample-"
    ]
    
    for indicator in simulation_indicators:
        if indicator in response:
            validation_result["potential_simulation"] = True
            validation_result["warnings"].append(f"检测到可能的模拟数据: {indicator}")
    
    # 判断是否为真实数据
    if (validation_result["has_tool_calls"] and 
        validation_result["has_observations"] and 
        not validation_result["potential_simulation"]):
        validation_result["is_real_data"] = True
    
    return validation_result


def format_real_data_only(raw_response: str, validation: Dict[str, Any]) -> str:
    """
    只格式化真实数据，过滤模拟内容
    
    Args:
        raw_response: 原始响应
        validation: 验证结果
        
    Returns:
        格式化后的真实数据
    """
    if not validation["is_real_data"]:
        warning_msg = "⚠️ 警告: 检测到可能的模拟数据或工具调用失败\n"
        if validation["warnings"]:
            warning_msg += "具体问题:\n"
            for warning in validation["warnings"]:
                warning_msg += f"  - {warning}\n"
        warning_msg += "\n请检查MCP工具是否正常工作，或联系管理员确认数据来源。"
        return warning_msg
    
    # 提取真实的观察数据
    if "Observation:" in raw_response:
        parts = raw_response.split("Observation:")
        if len(parts) > 1:
            observation_part = parts[1].split("Thought:")[0].strip()
            return f"📋 真实MCP工具返回数据:\n{observation_part}"
    
    return raw_response
