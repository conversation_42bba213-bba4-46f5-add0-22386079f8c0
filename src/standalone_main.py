#!/usr/bin/env python3
"""
独立主程序 - 不依赖MCP服务器
专注于展示LLM配置系统的功能
"""

import asyncio
import os
from dotenv import load_dotenv
from .llm_config import create_k8s_llm, print_provider_status, get_current_provider


async def test_llm_basic_functionality():
    """测试LLM基本功能"""
    print("🧪 测试LLM基本功能...")
    
    try:
        # 创建LLM实例
        llm = create_k8s_llm("production")
        
        # 测试简单对话
        print("💬 测试简单对话...")
        response = await llm.ainvoke("你好，请简单介绍一下你自己，用一句话回答。")
        print(f"🤖 LLM回复: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM测试失败: {e}")
        return False


async def test_kubernetes_scenarios():
    """测试Kubernetes相关场景"""
    print("\n⚙️  测试Kubernetes场景...")
    
    try:
        # 创建K8s专用LLM
        k8s_llm = create_k8s_llm("production")
        
        # K8s相关问题
        k8s_questions = [
            "如何查看Pod状态？用一句话回答。",
            "什么是Kubernetes Service？简单解释。",
            "如何创建一个Deployment？概述步骤。"
        ]
        
        for i, question in enumerate(k8s_questions, 1):
            print(f"\n📋 问题 {i}: {question}")
            try:
                response = await k8s_llm.ainvoke(question)
                # 截取前200个字符避免输出过长
                content = response.content[:200] + "..." if len(response.content) > 200 else response.content
                print(f"💡 回答: {content}")
            except Exception as e:
                print(f"❌ 问题 {i} 失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Kubernetes测试失败: {e}")
        return False


async def test_provider_switching():
    """测试提供商切换功能"""
    print("\n🔄 测试提供商切换功能...")
    
    try:
        current_provider = get_current_provider()
        print(f"📋 当前提供商: {current_provider.upper()}")
        
        # 测试不同环境的LLM创建
        environments = ["production", "development", "analysis"]
        
        for env in environments:
            try:
                llm = create_k8s_llm(env)
                print(f"✅ {env} 环境LLM创建成功")
                print(f"   - 模型: {llm.model_name}")
                print(f"   - 温度: {llm.temperature}")
                print(f"   - 最大Token: {llm.max_tokens}")
            except Exception as e:
                print(f"❌ {env} 环境LLM创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 提供商切换测试失败: {e}")
        return False


async def demonstrate_llm_features():
    """演示LLM特性"""
    print("\n🎯 演示LLM特性...")
    
    try:
        # 创建不同类型的LLM
        from .llm_config import create_llm
        
        llm_types = [
            ("default", "默认配置"),
            ("production", "生产环境"),
            ("development", "开发环境"),
            ("analysis", "分析任务")
        ]
        
        for llm_type, description in llm_types:
            try:
                llm = create_llm(model_type=llm_type)
                print(f"✅ {description}: {llm.model_name} (温度: {llm.temperature})")
            except Exception as e:
                print(f"❌ {description}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM特性演示失败: {e}")
        return False


async def main():
    """主函数"""
    # Load environment variables
    load_dotenv()
    
    print("🚀 独立LLM配置系统演示")
    print("=" * 60)
    print("📝 注意: 此版本不依赖MCP服务器，专注于LLM配置功能")
    print("=" * 60)
    
    # 显示当前配置状态
    print_provider_status()
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("基本功能测试", test_llm_basic_functionality),
        ("Kubernetes场景测试", test_kubernetes_scenarios),
        ("提供商切换测试", test_provider_switching),
        ("LLM特性演示", demonstrate_llm_features)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行 {test_name}...")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! LLM配置系统工作正常")
    else:
        print("⚠️  部分测试失败，请检查配置")
    
    print("\n💡 提示:")
    print("   - 如需切换提供商: uv run python switch_provider.py openrouter")
    print("   - 如需运行完整测试: uv run python run_tests.py")
    print("   - 如需查看配置状态: uv run python switch_provider.py status")


if __name__ == "__main__":
    asyncio.run(main())
