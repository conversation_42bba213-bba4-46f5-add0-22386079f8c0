"""
Qwen3工具调用适配器
解决Qwen3模型与OpenAI工具调用格式的兼容性问题
"""

import json
from typing import Any, Dict, List, Optional
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_openai import ChatOpenAI


class Qwen3ToolCallAdapter:
    """
    Qwen3工具调用适配器
    修复工具调用参数格式问题
    """
    
    def __init__(self, base_llm: ChatOpenAI):
        """
        初始化适配器
        
        Args:
            base_llm: 基础的ChatOpenAI实例
        """
        self.base_llm = base_llm
    
    def _fix_tool_call_arguments(self, tool_calls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        修复工具调用参数格式
        
        Args:
            tool_calls: 原始工具调用列表
            
        Returns:
            修复后的工具调用列表
        """
        fixed_calls = []
        
        for call in tool_calls:
            fixed_call = call.copy()
            
            if "function" in fixed_call:
                function = fixed_call["function"]
                
                # 修复arguments字段
                if "arguments" in function:
                    args = function["arguments"]
                    
                    # 如果arguments是None或空，设置为空对象的JSON字符串
                    if args is None:
                        function["arguments"] = "{}"
                    elif isinstance(args, dict):
                        # 如果是字典，转换为JSON字符串
                        function["arguments"] = json.dumps(args, ensure_ascii=False)
                    elif not isinstance(args, str):
                        # 如果不是字符串，尝试转换
                        try:
                            function["arguments"] = json.dumps(args, ensure_ascii=False)
                        except:
                            function["arguments"] = "{}"
                    
                    # 确保是有效的JSON字符串
                    try:
                        json.loads(function["arguments"])
                    except:
                        function["arguments"] = "{}"
            
            fixed_calls.append(fixed_call)
        
        return fixed_calls
    
    def _create_compatible_prompt(self, messages: List[Dict[str, Any]], tools: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        创建Qwen3兼容的提示词格式
        
        Args:
            messages: 原始消息列表
            tools: 工具定义列表
            
        Returns:
            兼容的消息列表
        """
        if not tools:
            return messages
        
        # 为Qwen3添加工具调用指导
        system_guidance = """
当需要调用工具时，请严格按照以下JSON格式输出：

```json
{
    "tool_calls": [
        {
            "id": "call_xxx",
            "type": "function",
            "function": {
                "name": "function_name",
                "arguments": "{\"param1\": \"value1\", \"param2\": \"value2\"}"
            }
        }
    ]
}
```

重要提示：
1. arguments字段必须是JSON字符串格式，不能是null或对象
2. 即使没有参数，也要使用 "{}" 作为arguments的值
3. 所有字符串值都要用双引号包围
"""
        
        enhanced_messages = []
        
        # 添加或增强系统消息
        has_system = any(msg.get("role") == "system" for msg in messages)
        
        if has_system:
            for msg in messages:
                if msg.get("role") == "system":
                    enhanced_msg = msg.copy()
                    enhanced_msg["content"] = msg["content"] + "\n\n" + system_guidance
                    enhanced_messages.append(enhanced_msg)
                else:
                    enhanced_messages.append(msg)
        else:
            # 添加新的系统消息
            enhanced_messages.append({
                "role": "system",
                "content": system_guidance
            })
            enhanced_messages.extend(messages)
        
        return enhanced_messages


def create_qwen3_compatible_llm(base_llm: ChatOpenAI) -> ChatOpenAI:
    """
    创建Qwen3兼容的LLM实例
    
    Args:
        base_llm: 基础ChatOpenAI实例
        
    Returns:
        兼容的ChatOpenAI实例
    """
    
    # 创建适配器
    adapter = Qwen3ToolCallAdapter(base_llm)
    
    # 重写模型配置以提高兼容性
    compatible_kwargs = {
        "model": base_llm.model_name,
        "api_key": base_llm.openai_api_key,
        "base_url": base_llm.openai_api_base,
        "max_tokens": base_llm.max_tokens,
        "temperature": base_llm.temperature,
        "top_p": getattr(base_llm, 'top_p', 0.1),
        "frequency_penalty": getattr(base_llm, 'frequency_penalty', 0.0),
        "presence_penalty": getattr(base_llm, 'presence_penalty', 0.0),
        "max_retries": getattr(base_llm, 'max_retries', 3),
        "request_timeout": getattr(base_llm, 'request_timeout', 120),
        "streaming": False,  # 禁用流式输出以提高稳定性
        "model_kwargs": {
            "tool_choice": "auto",
            "parallel_tool_calls": False,  # 禁用并行调用
            "response_format": {"type": "text"},  # 明确指定响应格式
        }
    }
    
    return ChatOpenAI(**compatible_kwargs)


def create_conservative_qwen3_llm(base_llm: ChatOpenAI) -> ChatOpenAI:
    """
    创建保守的Qwen3 LLM实例（禁用工具调用）
    
    Args:
        base_llm: 基础ChatOpenAI实例
        
    Returns:
        保守配置的ChatOpenAI实例
    """
    
    conservative_kwargs = {
        "model": base_llm.model_name,
        "api_key": base_llm.openai_api_key,
        "base_url": base_llm.openai_api_base,
        "max_tokens": base_llm.max_tokens,
        "temperature": base_llm.temperature,
        "top_p": getattr(base_llm, 'top_p', 0.1),
        "frequency_penalty": getattr(base_llm, 'frequency_penalty', 0.0),
        "presence_penalty": getattr(base_llm, 'presence_penalty', 0.0),
        "max_retries": getattr(base_llm, 'max_retries', 3),
        "request_timeout": getattr(base_llm, 'request_timeout', 120),
        "streaming": False,
        "model_kwargs": {
            "tool_choice": "none",  # 完全禁用工具调用
            "parallel_tool_calls": False,
        }
    }
    
    return ChatOpenAI(**conservative_kwargs)
