#!/usr/bin/env python3
"""
Gemini 2.5 Flash 大上下文能力测试
验证1,048,576 tokens输入上下文在K8s运维场景中的应用
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from mcp_use import MCPAgent, MCPClient
from src.llm_config import create_large_context_llm, get_context_info, create_llm


async def test_large_context_capability():
    """测试大上下文处理能力"""
    print("🧪 Gemini 2.5 Flash 大上下文能力测试")
    print("=" * 60)
    
    # 显示上下文能力信息
    context_info = get_context_info()
    print(f"📏 输入上下文长度: {context_info['input_context_length']:,} tokens")
    print(f"📋 能力描述: {context_info['input_context_description']}")
    print(f"🎯 应用场景:")
    for use_case in context_info['use_cases']:
        print(f"   - {use_case}")
    print()
    
    # 测试不同配置的输出能力
    print("🔧 输出配置对比:")
    for env, tokens in context_info['output_configurations'].items():
        print(f"   {env}: {tokens:,} tokens")
    print()
    
    return True


async def test_large_context_k8s_scenario():
    """测试大上下文在K8s场景中的应用"""
    print("🐳 大上下文K8s运维场景测试")
    print("=" * 60)
    
    try:
        # 使用内网MCP服务器配置
        config = {
            "mcpServers": {
                "k8s": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }
        
        # 创建MCP客户端
        print("🔗 创建MCP客户端...")
        client = MCPClient.from_dict(config)
        
        # 创建大上下文LLM实例
        print("🚀 创建大上下文LLM实例...")
        llm = create_large_context_llm(max_tokens=16384)
        
        print(f"✅ 大上下文LLM创建成功:")
        print(f"   模型: {llm.model_name}")
        print(f"   最大输出: {llm.max_tokens:,} tokens")
        print(f"   超时设置: {llm.request_timeout}s")
        print()
        
        # 创建Agent
        print("🤖 创建大上下文MCP Agent...")
        agent = MCPAgent(llm=llm, client=client, max_steps=5)
        
        # 测试复杂的多集群分析查询
        print("💬 测试复杂的多集群分析查询...")
        complex_query = """
        请帮我进行一个全面的K8s集群分析：
        
        1. 首先获取所有可用集群的列表
        2. 选择其中2-3个集群，获取它们的详细信息
        3. 对比这些集群的配置差异
        4. 分析每个集群的资源使用情况
        5. 提供优化建议和潜在问题识别
        
        请提供一个详细的分析报告，包括：
        - 集群基本信息对比表
        - 配置差异分析
        - 资源使用状况
        - 安全配置检查
        - 优化建议
        """
        
        result = await agent.run(complex_query, max_steps=8)
        
        print("📋 大上下文分析结果:")
        print("-" * 50)
        print(result)
        print("-" * 50)
        
        # 计算结果长度
        result_length = len(result)
        estimated_tokens = result_length // 4  # 粗略估算token数
        
        print(f"📊 结果统计:")
        print(f"   字符数: {result_length:,}")
        print(f"   估算tokens: {estimated_tokens:,}")
        print(f"   利用率: {(estimated_tokens/16384)*100:.1f}% (基于16K输出限制)")
        
        return True
        
    except Exception as e:
        print(f"❌ 大上下文测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_context_scaling():
    """测试不同上下文配置的性能"""
    print("\n⚡ 上下文配置性能测试")
    print("=" * 60)
    
    configurations = [
        ("标准配置", "production"),
        ("开发配置", "development"), 
        ("分析配置", "analysis"),
        ("大上下文配置", "large_context")
    ]
    
    for config_name, environment in configurations:
        try:
            print(f"🧪 测试 {config_name} ({environment})...")
            
            if environment == "large_context":
                llm = create_large_context_llm()
            else:
                # 所有环境现在都使用相同的最大能力配置
                llm = create_llm()
            
            print(f"   ✅ 配置成功:")
            print(f"      最大输出: {llm.max_tokens:,} tokens")
            print(f"      超时设置: {llm.request_timeout}s")
            print(f"      温度: {llm.temperature}")
            
        except Exception as e:
            print(f"   ❌ 配置失败: {e}")
    
    return True


async def demonstrate_large_context_use_cases():
    """演示大上下文的具体使用场景"""
    print("\n🎯 大上下文使用场景演示")
    print("=" * 60)
    
    use_cases = [
        {
            "name": "大量日志分析",
            "description": "分析多个Pod的完整日志，识别错误模式",
            "input_size": "~500K tokens",
            "query": "分析所有Pod的日志，找出错误模式和性能瓶颈"
        },
        {
            "name": "复杂配置对比",
            "description": "对比多个集群的完整配置文件",
            "input_size": "~300K tokens", 
            "query": "对比3个集群的完整配置，识别配置差异和安全风险"
        },
        {
            "name": "全集群状态分析",
            "description": "分析整个集群的所有资源状态",
            "input_size": "~800K tokens",
            "query": "生成完整的集群健康报告，包括所有资源状态"
        },
        {
            "name": "故障排查报告",
            "description": "基于大量诊断数据生成详细报告",
            "input_size": "~400K tokens",
            "query": "基于收集的所有诊断信息，生成详细的故障排查报告"
        }
    ]
    
    for i, use_case in enumerate(use_cases, 1):
        print(f"{i}. {use_case['name']}")
        print(f"   📝 描述: {use_case['description']}")
        print(f"   📏 输入规模: {use_case['input_size']}")
        print(f"   💬 示例查询: {use_case['query']}")
        print()
    
    print("💡 这些场景都可以在单次对话中完成，无需分割处理！")
    return True


async def main():
    """主测试函数"""
    print("🚀 Gemini 2.5 Flash 大上下文能力全面测试")
    print("=" * 70)
    
    # 加载环境变量
    load_dotenv()
    
    # 运行测试
    tests = [
        ("大上下文能力展示", test_large_context_capability),
        ("K8s运维场景测试", test_large_context_k8s_scenario),
        ("配置性能测试", test_context_scaling),
        ("使用场景演示", demonstrate_large_context_use_cases),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行 {test_name}...")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 完成")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 70)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 Gemini 2.5 Flash 大上下文能力验证成功!")
        print("\n💡 关键优势:")
        print("   - 1,048,576 tokens 输入上下文")
        print("   - 最高32K tokens 输出能力")
        print("   - 适合复杂K8s运维场景")
        print("   - 无需分割处理大型数据")
    else:
        print("⚠️  部分测试未通过，请检查配置")


if __name__ == "__main__":
    asyncio.run(main())
