--- a/pkg/handlers/base/utility_handler.go
+++ b/pkg/handlers/base/utility_handler.go
@@ -63,11 +63,12 @@ func (h *UtilityHandler) Register(server *server.MCPServer) {
 
 	if h.Switcher.Get {
 		// 获取集群信息工具
-		server.AddTool(mcp.NewTool(GET_CLUSTER_INFO,
+		clusterInfoTool := mcp.NewTool(GET_CLUSTER_INFO,
 			mcp.WithDescription("Get Kubernetes cluster information"),
 			mcp.WithString("cluster",
 				mcp.Description("Cluster name"),
 				mcp.Required(),
 			),
-		), h.GetClusterInfo)
+		)
+		server.AddTool(h.makeStrictTool(clusterInfoTool), h.GetClusterInfo)
 
 		// 获取API资源工具
-		server.AddTool(mcp.NewTool(GET_API_RESOURCES,
+		apiResourcesTool := mcp.NewTool(GET_API_RESOURCES,
 			mcp.WithDescription("Get available API resources in the cluster"),
 			mcp.WithString("cluster",
 				mcp.Description("Cluster name"),
 				mcp.Required(),
 			),
 			mcp.WithString("group",
 				mcp.Description("API Group (optional)"),
 			),
-		), h.GetAPIResources)
+		)
+		server.AddTool(h.makeStrictTool(apiResourcesTool), h.GetAPIResources)
 	}
 
 	if h.Switcher.List {
 		// 搜索资源工具
-		server.AddTool(mcp.NewTool(SEARCH_RESOURCES,
+		searchTool := mcp.NewTool(SEARCH_RESOURCES,
 			mcp.WithDescription("Search resources across the cluster"),
 			mcp.WithString("cluster",
 				mcp.Description("Cluster name"),
 				mcp.Required(),
 			),
 			mcp.WithString("query",
 				mcp.Description("Search query (name, label, annotation pattern)"),
 				mcp.Required(),
 			),
 			mcp.WithString("namespaces",
 				mcp.Description("Comma-separated list of namespaces to search (default: all)"),
 			),
 			mcp.WithString("kinds",
 				mcp.Description("Comma-separated list of resource kinds to search (default: all)"),
 			),
 			mcp.WithBoolean("matchLabels",
 				mcp.Description("Whether to match labels in search"),
 				mcp.DefaultBool(true),
 			),
 			mcp.WithBoolean("matchAnnotations",
 				mcp.Description("Whether to match annotations in search"),
 				mcp.DefaultBool(true),
 			),
-		), h.SearchResources)
+		)
+		server.AddTool(h.makeStrictTool(searchTool), h.SearchResources)
 	}
 
 	// ... 继续为其他工具添加相同的模式
 }
 
+// makeStrictTool 为工具添加 strict: true 参数
+func (h *UtilityHandler) makeStrictTool(tool mcp.Tool) mcp.Tool {
+	// 创建一个新的工具定义，添加 strict: true
+	// 注意：这需要修改 mcp.Tool 结构或使用反射
+	// 这是一个概念性的实现，实际实现可能需要不同的方法
+	
+	// 方法1: 如果mcp-go支持自定义JSON marshaling
+	// 我们可能需要创建一个包装器或修改库
+	
+	// 方法2: 使用 NewToolWithRawSchema 创建带有 strict 的工具
+	strictSchema := map[string]interface{}{
+		"type": "object",
+		"strict": true,
+		"properties": tool.InputSchema.Properties,
+		"required": tool.InputSchema.Required,
+	}
+	
+	schemaBytes, _ := json.Marshal(strictSchema)
+	return mcp.NewToolWithRawSchema(tool.Name, tool.Description, json.RawMessage(schemaBytes))
+}
