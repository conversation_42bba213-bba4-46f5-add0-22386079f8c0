FROM docker2.gf.com.cn/library/golang:v1.0.0-1.23-alpine3.19 AS builder


WORKDIR /app

COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go mod tidy

RUN CGO_ENABLED=0 GOOS=${TARGETOS} GOARCH=${TARGETARCH} go build \
    -ldflags="-w -s \
    -X code.gf.com.cn/gf/nccp/k8s-mcp/cmd/k8s-mcp/app.Version=${VERSION} \
    -X code.gf.com.cn/gf/nccp/k8s-mcp/cmd/k8s-mcp/app.Commit=${COMMIT} \
    -X code.gf.com.cn/gf/nccp/k8s-mcp/cmd/k8s-mcp/app.BuildDate=${BUILD_DATE}" \
    -o /app/k8s-mcp \
    ./cmd/k8s-mcp/main.go

FROM docker2.gf.com.cn/library/alpine:v1.0.0-3.19

WORKDIR /app

COPY --from=builder /app/k8s-mcp /app/k8s-mcp


EXPOSE 8080

ENTRYPOINT ["/app/k8s-mcp", "server", "transport", "sse", "--port=8080", "--health-port=9090", "--log-level=debug"]
