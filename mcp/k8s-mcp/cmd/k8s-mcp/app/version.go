package app

import (
	"fmt"

	"k8s-mcp/pkg/logger"

	"github.com/spf13/cobra"
)

// 版本信息
var (
	Version   = "1.0.0"
	Commit    = "none"
	BuildDate = "unknown"
)

// Logo ASCII 艺术字
const logo = `
 _   __ _____        ___  ___ _____ ______    _____                                
| | / /|  _  |       |  \/  |/  __ \| ___ \  /  ___|                               
| |/ /  \ V /  ___   | .  . || /  \/| |_/ /  \ '--.   ___  _ __ __   __  ___  _ __ 
|    \  / _ \ / __|  | |\/| || |    |  __/    '--. \ / _ \| '__|\ \ / / / _ \| '__|
| |\  \| |_| |\__ \  | |  | || \__/\| |      /\__/ /|  __/| |    \ V / |  __/| |   
\_| \_/\_____/|___/  \_|  |_/ \____/\_|      \____/  \___||_|     \_/   \___||_|   
`

func NewVersionCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "version",
		Short: "Print version information",
		Run: func(cmd *cobra.Command, args []string) {
			log := logger.GetLogger()

			// 先打印 logo
			fmt.Print(logo)

			// 打印版本信息
			versionInfo := fmt.Sprintf("k8s-mcp version %s (commit: %s, build date: %s)\n",
				Version, Commit, BuildDate)

			fmt.Print(versionInfo)

			log.Info("Version info displayed",
				"version", Version,
				"commit", Commit,
				"buildDate", BuildDate,
			)
		},
	}

	return cmd
}
