package app

import (
	"k8s-mcp/pkg/config"
	"k8s-mcp/pkg/logger"

	"github.com/spf13/cobra"
)

func NewRootCommand(cfg *config.Config) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "k8s-mcp",
		Short: "Kubernetes MCP server",
		Long:  `A server that implements Model Capable Protocol (MCP) for Kubernetes operations.`,
		PersistentPreRun: func(cmd *cobra.Command, args []string) {
			// 更新日志级别
			logger.InitializeDefaultLogger(cfg.LogLevel, cfg.LogFormat)
		},
	}

	// 全局标志
	cmd.PersistentFlags().StringVar(&cfg.LogLevel, "log-level", cfg.LogLevel, "Log level (debug, info, warn, error)")
	cmd.PersistentFlags().StringVar(&cfg.LogFormat, "log-format", cfg.LogFormat, "Log format (console, json)")
	cmd.PersistentFlags().BoolVar(&cfg.Switcher.List, "allow-list", cfg.Switcher.List, "Allow list resource operation (true/false), default is true")
	cmd.PersistentFlags().BoolVar(&cfg.Switcher.Get, "allow-get", cfg.Switcher.Get, "Allow get resource operation (true/false), default is true")
	cmd.PersistentFlags().BoolVar(&cfg.Switcher.Describe, "allow-describe", cfg.Switcher.Describe, "Allow describe resource operation (true/false), default is true")
	cmd.PersistentFlags().BoolVar(&cfg.Switcher.Metrics, "allow-metrics", cfg.Switcher.Metrics, "Allow get metrics of resource (true/false), default is true")
	cmd.PersistentFlags().BoolVar(&cfg.Switcher.Prompt, "allow-prompt", cfg.Switcher.Prompt, "Allow load prompts (true/false), default is true")
	cmd.PersistentFlags().BoolVar(&cfg.Switcher.Utility, "allow-utility", cfg.Switcher.Utility, "Allow use utilites (true/false), default is true")
	cmd.PersistentFlags().BoolVar(&cfg.Switcher.Apply, "allow-apply", cfg.Switcher.Apply, "Allow apply yaml (true/false), default is false")
	cmd.PersistentFlags().BoolVar(&cfg.Switcher.Create, "allow-create", cfg.Switcher.Create, "Allow create resource operation (true/false), default is false")
	cmd.PersistentFlags().BoolVar(&cfg.Switcher.Update, "allow-update", cfg.Switcher.Update, "Allow update resource operation (true/false), default is false")
	cmd.PersistentFlags().BoolVar(&cfg.Switcher.Patch, "allow-patch", cfg.Switcher.Patch, "Allow patch resource operation (true/false), default is false")
	cmd.PersistentFlags().BoolVar(&cfg.Switcher.Delete, "allow-delete", cfg.Switcher.Delete, "Allow delete resource operation (true/false), default is false")
	cmd.PersistentFlags().BoolVar(&cfg.Switcher.Rollout, "allow-rollout", cfg.Switcher.Rollout, "Allow rollout restart resource operation (true/false), default is false")

	// 添加子命令
	cmd.AddCommand(NewServerCommand(cfg))
	cmd.AddCommand(NewVersionCommand())

	return cmd
}
