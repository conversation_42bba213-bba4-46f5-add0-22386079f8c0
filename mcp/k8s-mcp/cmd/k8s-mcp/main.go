package main

import (
	"os"

	"k8s-mcp/cmd/k8s-mcp/app"
	"k8s-mcp/pkg/cce"
	"k8s-mcp/pkg/client"
	"k8s-mcp/pkg/config"
	"k8s-mcp/pkg/logger"
)

func main() {
	// 初始化配置
	cfg := config.NewDefaultConfig()

	// 初始化日志
	logger.InitializeDefaultLogger(cfg.LogLevel, cfg.LogFormat)
	log := logger.GetLogger()

	// 初始化k8s客户端
	if err := client.InitializeAllClients(cfg); err != nil {
		log.Error("Failed to initialize Kubernetes client", "error", err)
		os.Exit(1)
	}

	// 初始化cce客户端
	if err := cce.InitializeCceClient(cfg); err != nil {
		log.Error("Failed to initialize CCE client", "error", err)
		os.Exit(1)
	}

	// 创建命令行应用
	rootCmd := app.NewRootCommand(cfg)

	// 执行根命令
	if err := rootCmd.Execute(); err != nil {
		log.Error("Failed to execute root command", "error", err)
		os.Exit(1)
	}
}
