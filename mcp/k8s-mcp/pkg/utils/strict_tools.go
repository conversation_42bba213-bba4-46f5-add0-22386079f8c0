package utils

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

// StrictToolWrapper 包装MCP服务器，自动为所有工具添加strict模式
type StrictToolWrapper struct {
	*server.MCPServer
}

// NewStrictMCPServer 创建一个自动添加strict模式的MCP服务器包装器
func NewStrictMCPServer(baseServer *server.MCPServer) *StrictToolWrapper {
	return &StrictToolWrapper{
		MCPServer: baseServer,
	}
}

// AddTool 重写AddTool方法，自动为工具添加strict模式
func (s *StrictToolWrapper) AddTool(tool mcp.Tool, handler func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)) {
	// 转换工具为strict模式
	strictTool := s.makeToolStrict(tool)
	
	// 调用原始的AddTool方法
	s.MCPServer.AddTool(strictTool, handler)
}

// makeToolStrict 将普通工具转换为strict模式工具
func (s *StrictToolWrapper) makeToolStrict(tool mcp.Tool) mcp.Tool {
	// 如果工具已经使用RawInputSchema，尝试添加strict
	if len(tool.RawInputSchema) > 0 {
		return s.addStrictToRawSchema(tool)
	}
	
	// 否则从InputSchema创建strict版本
	return s.createStrictFromInputSchema(tool)
}

// addStrictToRawSchema 为已有的RawInputSchema添加strict字段
func (s *StrictToolWrapper) addStrictToRawSchema(tool mcp.Tool) mcp.Tool {
	var schema map[string]interface{}
	if err := json.Unmarshal(tool.RawInputSchema, &schema); err != nil {
		// 如果解析失败，返回原工具
		return tool
	}
	
	// 添加strict字段
	schema["strict"] = true
	schema["additionalProperties"] = false
	
	// 重新序列化
	strictSchemaBytes, err := json.Marshal(schema)
	if err != nil {
		return tool
	}
	
	return mcp.NewToolWithRawSchema(tool.Name, tool.Description, json.RawMessage(strictSchemaBytes))
}

// createStrictFromInputSchema 从InputSchema创建strict版本
func (s *StrictToolWrapper) createStrictFromInputSchema(tool mcp.Tool) mcp.Tool {
	// 创建strict schema
	strictSchema := map[string]interface{}{
		"type":                 "object",
		"strict":               true,
		"additionalProperties": false,
		"properties":           tool.InputSchema.Properties,
		"required":             tool.InputSchema.Required,
	}
	
	// 如果没有properties，创建一个空的
	if strictSchema["properties"] == nil {
		strictSchema["properties"] = map[string]interface{}{}
	}
	
	// 如果没有required，创建一个空的
	if strictSchema["required"] == nil {
		strictSchema["required"] = []string{}
	}
	
	schemaBytes, err := json.Marshal(strictSchema)
	if err != nil {
		// 如果序列化失败，返回原工具
		return tool
	}
	
	return mcp.NewToolWithRawSchema(tool.Name, tool.Description, json.RawMessage(schemaBytes))
}

// ConvertToolToStrict 公共函数，将任何工具转换为strict模式
func ConvertToolToStrict(tool mcp.Tool) mcp.Tool {
	wrapper := &StrictToolWrapper{}
	return wrapper.makeToolStrict(tool)
}

// BatchConvertToolsToStrict 批量转换工具为strict模式
func BatchConvertToolsToStrict(tools []mcp.Tool) []mcp.Tool {
	strictTools := make([]mcp.Tool, len(tools))
	for i, tool := range tools {
		strictTools[i] = ConvertToolToStrict(tool)
	}
	return strictTools
}

// IsToolStrict 检查工具是否已经是strict模式
func IsToolStrict(tool mcp.Tool) bool {
	if len(tool.RawInputSchema) > 0 {
		var schema map[string]interface{}
		if err := json.Unmarshal(tool.RawInputSchema, &schema); err != nil {
			return false
		}
		
		strict, exists := schema["strict"]
		if !exists {
			return false
		}
		
		strictBool, ok := strict.(bool)
		return ok && strictBool
	}
	
	return false
}

// ValidateStrictSchema 验证strict schema是否符合OpenAI要求
func ValidateStrictSchema(schema map[string]interface{}) error {
	// 检查必需的字段
	if schema["type"] != "object" {
		return fmt.Errorf("strict schema must have type 'object'")
	}
	
	if strict, exists := schema["strict"]; !exists || strict != true {
		return fmt.Errorf("strict schema must have 'strict': true")
	}
	
	if additionalProps, exists := schema["additionalProperties"]; !exists || additionalProps != false {
		return fmt.Errorf("strict schema must have 'additionalProperties': false")
	}
	
	// 检查properties是否存在
	if _, exists := schema["properties"]; !exists {
		return fmt.Errorf("strict schema must have 'properties' field")
	}
	
	// 检查required是否存在
	if _, exists := schema["required"]; !exists {
		return fmt.Errorf("strict schema must have 'required' field")
	}
	
	return nil
}
