package handlers

import (
	"github.com/mark3labs/mcp-go/server"

	"k8s-mcp/pkg/config"
	"k8s-mcp/pkg/handlers/interfaces"
	"k8s-mcp/pkg/logger"
)

// HandlerProviderImpl 实现HandlerProvider接口
type HandlerProviderImpl struct {
	handlers []interfaces.ToolHandler
}

// 确保实现了接口
var _ interfaces.HandlerProvider = &HandlerProviderImpl{}

// GetHandlers 实现接口方法
func (p *HandlerProviderImpl) GetHandlers() []interfaces.ToolHandler {
	return p.handlers
}

// RegisterAllHandlers 实现接口方法
func (p *HandlerProviderImpl) RegisterAllHandlers(server *server.MCPServer) {
	log := logger.GetLogger()

	// 注册所有处理程序
	for _, handler := range p.handlers {
		handler.Register(server)
	}

	log.Info("All handlers registered")
}

// NewHandlerProvider 创建新的处理程序提供者
func NewHandlerProvider(cfg *config.Config) interfaces.HandlerProvider {
	// 使用工厂创建所有处理程序
	factory := NewHandlerFactory()

	// 按照API组和Version组织处理程序
	handlers := []interfaces.ToolHandler{
		// 集群级别资源
		factory.CreateNamespaceHandler(cfg), // 集群作用域, v1 (core)
		factory.CreateNodeHandler(cfg),      // 集群作用域, v1 (core)

		// 核心API组 (v1)
		factory.CreateCoreHandler(cfg),

		// apps API组 (apps/v1)
		factory.CreateAppsHandler(cfg),

		// batch API组 (batch/v1)
		factory.CreateBatchHandler(cfg),

		// networking API组 (networking.k8s.io/v1)
		factory.CreateNetworkingHandler(cfg),

		// storage API组 (storage.k8s.io/v1)
		factory.CreateStorageHandler(cfg),

		// rbac API组 (rbac.authorization.k8s.io/v1)
		factory.CreateRbacHandler(cfg),

		// policy API组 (policy/v1beta1)
		factory.CreatePolicyHandler(cfg),

		// apiextensions API组 (apiextensions.k8s.io/v1)
		factory.CreateApiExtensionsHandler(cfg),

		// autoscaling API组 (autoscaling/v1)
		factory.CreateAutoscalingHandler(cfg),

		// 通用工具处理程序
		factory.CreateUtilityHandler(cfg),

		// 提示词处理程序
		factory.CreatePromptHandler(cfg),

		// 指标处理程序
		factory.CreateMetricsHandler(cfg),

		// 平台处理程序
		factory.CreatePlatformHandler(cfg),
	}

	return &HandlerProviderImpl{
		handlers: handlers,
	}
}
