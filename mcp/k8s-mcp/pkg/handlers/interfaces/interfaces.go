package interfaces

import (
	"context"
	"k8s-mcp/pkg/config"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

// ResourceScope 定义资源的作用域
type ResourceScope string

const (
	PlatformScope ResourceScope = "platform"
	// ClusterScope 表示集群作用域资源
	ClusterScope ResourceScope = "cluster"
	// NamespaceScope 表示命名空间作用域资源
	NamespaceScope ResourceScope = "namespace"
)

// APIGroup 定义资源的API组
type APIGroup string

const (
	// CoreAPIGroup 代表核心API组 (v1)
	CoreAPIGroup APIGroup = "core"
	// AppsAPIGroup 代表应用API组 (apps/v1)
	AppsAPIGroup APIGroup = "apps"
	// BatchAPIGroup 代表批处理API组 (batch/v1)
	BatchAPIGroup APIGroup = "batch"
	// NetworkingAPIGroup 代表网络API组 (networking.k8s.io/v1)
	NetworkingAPIGroup APIGroup = "networking.k8s.io"
	// RbacAPIGroup 代表RBAC API组 (rbac.authorization.k8s.io/v1)
	RbacAPIGroup APIGroup = "rbac.authorization.k8s.io"
	// StorageAPIGroup 代表存储API组 (storage.k8s.io/v1)
	StorageAPIGroup APIGroup = "storage.k8s.io"
	// ApiextensionsAPIGroup 代表API扩展API组 (apiextensions.k8s.io/v1)
	ApiextensionsAPIGroup APIGroup = "apiextensions.k8s.io"
	// PolicyAPIGroup 代表策略API组 (policy/v1beta1)
	PolicyAPIGroup APIGroup = "policy"
	// AutoscalingAPIGroup 代表自动伸缩API组 (autoscaling/v1)
	AutoscalingAPIGroup APIGroup = "autoscaling"
)

// ToolHandler 定义MCP工具处理接口
type ToolHandler interface {
	// Handle 处理工具请求
	Handle(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)

	// Register 注册工具到MCP服务器
	Register(server *server.MCPServer)

	// GetScope 返回处理程序的作用域（集群或命名空间）
	GetScope() ResourceScope

	// GetAPIGroup 返回处理程序的API组
	GetAPIGroup() APIGroup
}

// ResourceHandler 定义资源处理接口
type ResourceHandler interface {
	ToolHandler

	// ListResources 列出资源
	ListResources(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)

	// GetResource 获取资源
	GetResource(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)

	// DescribeResource 详细描述资源
	DescribeResource(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)

	// CreateResource 创建资源
	CreateResource(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)

	// UpdateResource 更新资源
	UpdateResource(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)

	// PatchResource 修补资源
	PatchResource(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)

	// DeleteResource 删除资源
	DeleteResource(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)
}

// NamespaceHandler 定义命名空间处理接口
type NamespaceHandler interface {
	ToolHandler

	// ListNamespaces 列出命名空间
	ListNamespaces(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)
}

// HandlerProvider 提供所有工具处理程序
type HandlerProvider interface {
	// GetHandlers 返回所有处理程序
	GetHandlers() []ToolHandler

	// RegisterAllHandlers 注册所有处理程序
	RegisterAllHandlers(server *server.MCPServer)
}

// HandlerFactory 提供创建各种资源处理程序的工厂方法
type HandlerFactory interface {
	// CreateCoreHandler 创建核心资源处理程序
	CreateCoreHandler(cfg *config.Config) ResourceHandler

	// CreateAppsHandler 创建应用资源处理程序
	CreateAppsHandler(cfg *config.Config) ResourceHandler

	// CreateBatchHandler 创建批处理资源处理程序
	CreateBatchHandler(cfg *config.Config) ResourceHandler

	// CreateNetworkingHandler 创建网络资源处理程序
	CreateNetworkingHandler(cfg *config.Config) ResourceHandler

	// CreateStorageHandler 创建存储资源处理程序
	CreateStorageHandler(cfg *config.Config) ResourceHandler

	// CreateRbacHandler 创建RBAC资源处理程序
	CreateRbacHandler(cfg *config.Config) ResourceHandler

	// CreatePolicyHandler 创建策略资源处理程序
	CreatePolicyHandler(cfg *config.Config) ResourceHandler

	// CreateApiExtensionsHandler 创建API扩展资源处理程序
	CreateApiExtensionsHandler(cfg *config.Config) ResourceHandler

	// CreateAutoscalingHandler 创建自动伸缩资源处理程序
	CreateAutoscalingHandler(cfg *config.Config) ResourceHandler

	// CreateNamespaceHandler 创建命名空间处理程序
	CreateNamespaceHandler(cfg *config.Config) NamespaceHandler

	// CreateNodeHandler 创建节点处理程序
	CreateNodeHandler(cfg *config.Config) ToolHandler

	// CreateUtilityHandler 创建通用工具处理程序
	CreateUtilityHandler(cfg *config.Config) ToolHandler

	// CreatePromptHandler 创建提示词处理程序
	CreatePromptHandler(cfg *config.Config) ToolHandler

	// CreateMetricsHandler 创建指标处理程序
	CreateMetricsHandler(cfg *config.Config) ToolHandler

	// CreatePlatformHandler 创建平台处理沉痼
	CreatePlatformHandler(cfg *config.Config) ToolHandler
}

// BaseResourceHandler 定义资源处理器的基础实现
type BaseResourceHandler interface {
	ResourceHandler
	GetResourcePrefix() string
	GetNamespaceWithDefault(cluster string, incomingNamespace string) string
}
