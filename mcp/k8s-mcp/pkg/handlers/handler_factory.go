package handlers

import (
	"k8s-mcp/pkg/config"
	apiextensionsv1 "k8s-mcp/pkg/handlers/apis/apiextensions/v1"
	appsv1 "k8s-mcp/pkg/handlers/apis/apps/v1"
	autoscalingv1 "k8s-mcp/pkg/handlers/apis/autoscaling/v1"
	batchv1 "k8s-mcp/pkg/handlers/apis/batch/v1"
	networkingv1 "k8s-mcp/pkg/handlers/apis/networking/v1"
	policyv1beta1 "k8s-mcp/pkg/handlers/apis/policy/v1beta1"
	rbacv1 "k8s-mcp/pkg/handlers/apis/rbac/v1"
	storagev1 "k8s-mcp/pkg/handlers/apis/storage/v1"
	corev1 "k8s-mcp/pkg/handlers/apis/v1"
	"k8s-mcp/pkg/handlers/base"
	"k8s-mcp/pkg/handlers/interfaces"
)

// HandlerFactoryImpl 实现HandlerFactory接口
type HandlerFactoryImpl struct {
}

// 确保实现了接口
var _ interfaces.HandlerFactory = &HandlerFactoryImpl{}

// NewHandlerFactory 创建新的处理程序工厂
func NewHandlerFactory() interfaces.HandlerFactory {
	return &HandlerFactoryImpl{}
}

// CreateCoreHandler 创建核心资源处理程序
func (f *HandlerFactoryImpl) CreateCoreHandler(cfg *config.Config) interfaces.ResourceHandler {
	return corev1.NewResourceHandler(cfg)
}

// CreateAppsHandler 创建应用资源处理程序
func (f *HandlerFactoryImpl) CreateAppsHandler(cfg *config.Config) interfaces.ResourceHandler {
	return appsv1.NewResourceHandler(cfg)
}

// CreateBatchHandler 创建批处理资源处理程序
func (f *HandlerFactoryImpl) CreateBatchHandler(cfg *config.Config) interfaces.ResourceHandler {
	return batchv1.NewResourceHandler(cfg)
}

// CreateNetworkingHandler 创建网络资源处理程序
func (f *HandlerFactoryImpl) CreateNetworkingHandler(cfg *config.Config) interfaces.ResourceHandler {
	return networkingv1.NewResourceHandler(cfg)
}

// CreateStorageHandler 创建存储资源处理程序
func (f *HandlerFactoryImpl) CreateStorageHandler(cfg *config.Config) interfaces.ResourceHandler {
	return storagev1.NewResourceHandler(cfg)
}

// CreateRbacHandler 创建RBAC资源处理程序
func (f *HandlerFactoryImpl) CreateRbacHandler(cfg *config.Config) interfaces.ResourceHandler {
	return rbacv1.NewResourceHandler(cfg)
}

// CreatePolicyHandler 创建策略资源处理程序
func (f *HandlerFactoryImpl) CreatePolicyHandler(cfg *config.Config) interfaces.ResourceHandler {
	return policyv1beta1.NewResourceHandler(cfg)
}

// CreateApiExtensionsHandler 创建API扩展资源处理程序
func (f *HandlerFactoryImpl) CreateApiExtensionsHandler(cfg *config.Config) interfaces.ResourceHandler {
	return apiextensionsv1.NewResourceHandler(cfg)
}

// CreateAutoscalingHandler 创建自动伸缩资源处理程序
func (f *HandlerFactoryImpl) CreateAutoscalingHandler(cfg *config.Config) interfaces.ResourceHandler {
	return autoscalingv1.NewResourceHandler(cfg)
}

// CreateNamespaceHandler 创建命名空间处理程序
func (f *HandlerFactoryImpl) CreateNamespaceHandler(cfg *config.Config) interfaces.NamespaceHandler {
	return corev1.NewNamespaceHandler(cfg)
}

// CreateNodeHandler 创建节点处理程序
func (f *HandlerFactoryImpl) CreateNodeHandler(cfg *config.Config) interfaces.ToolHandler {
	return corev1.NewNodeHandler(cfg)
}

// CreateUtilityHandler 创建通用工具处理程序
func (f *HandlerFactoryImpl) CreateUtilityHandler(cfg *config.Config) interfaces.ToolHandler {
	return base.NewUtilityHandler(cfg)
}

// CreatePromptHandler 创建提示词处理程序
func (f *HandlerFactoryImpl) CreatePromptHandler(cfg *config.Config) interfaces.ToolHandler {
	return base.NewPromptHandler(cfg)
}

// CreateMetricsHandler 创建指标处理程序
func (f *HandlerFactoryImpl) CreateMetricsHandler(cfg *config.Config) interfaces.ToolHandler {
	return base.NewMetricsHandler(cfg)
}

// CreateMetricsHandler 创建指标处理程序
func (f *HandlerFactoryImpl) CreatePlatformHandler(cfg *config.Config) interfaces.ToolHandler {
	return base.NewPlatformHandler(cfg)
}
