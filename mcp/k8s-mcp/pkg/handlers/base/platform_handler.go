package base

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	client "k8s-mcp/pkg/cce"
	"k8s-mcp/pkg/config"
	"k8s-mcp/pkg/handlers/interfaces"
	"k8s-mcp/pkg/utils"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

// 定义常量
const (
	// cluster tools
	LIST_CLUSTERS    = "LIST_CLUSTERS"
	DESCRIBE_CLUSTER = "DESCRIBE_CLUSTER"
	// organization tools
	LIST_ORGANIZATIONS = "LIST_ORGANIZATIONS"
	DESC_ORGANIZATION  = "DESC_ORGANIZATION"
	// user tools
	LIST_USERS = "LIST_USERS"
)

// ClusterHandler 节点处理程序实现
type PlatformHandler struct {
	Handler
}

// 确保实现了接口
var _ interfaces.ToolHandler = (*PlatformHandler)(nil)

// NewClusterHandler 创建新的节点处理程序
func NewPlatformHandler(cfg *config.Config) interfaces.ToolHandler {
	return &PlatformHandler{
		Handler: NewBaseHandler(interfaces.PlatformScope, interfaces.CoreAPIGroup, cfg.Switcher),
	}
}

// Handle 实现接口方法
func (h *PlatformHandler) Handle(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 根据工具名称分派到具体的处理方法
	switch request.Method {
	case LIST_CLUSTERS:
		return h.ListClusters(ctx, request)
	case DESCRIBE_CLUSTER:
		return h.DescribeCluster(ctx, request)
	case LIST_ORGANIZATIONS:
		return h.ListOrganizations(ctx, request)
	case DESC_ORGANIZATION:
		return h.DescOrganization(ctx, request)
	case LIST_USERS:
		return h.ListUsers(ctx, request)
	default:
		return utils.NewErrorToolResult(fmt.Sprintf("unknown platform method: %s", request.Method)), nil
	}
}

// Register 实现接口方法
func (h *PlatformHandler) Register(server *server.MCPServer) {
	h.Log.Info("Registering platform handlers")

	if h.Switcher.List {
		// 注册列出集群列表工具
		server.AddTool(mcp.NewTool(LIST_CLUSTERS,
			mcp.WithDescription("List all clusters (Platform-scoped)"),
		), h.ListClusters)

		// 注册列出组织列表工具
		server.AddTool(mcp.NewTool(LIST_ORGANIZATIONS,
			mcp.WithDescription("List organizations (also means 'systems') by paging. "+
				"If you want to get all organizations, you need to call this tool repeatedly by "+
				"incrementing the page parameter until the returned pageStatus.is_end is true."),
			mcp.WithNumber("page",
				mcp.Description("Page number, default is 1"),
			),
			mcp.WithNumber("size",
				mcp.Description("Page size, default is 50"),
			),
			mcp.WithBoolean("detail",
				mcp.Description("Get detail of organization, default is 'true'"),
			),
			mcp.WithBoolean("withAdmin",
				mcp.Description("Get the administrators of organization, default is 'false'"),
			),
		), h.ListOrganizations)

		// 注册获取组织用户列表工具
		server.AddTool(mcp.NewTool(LIST_USERS,
			mcp.WithDescription("List the user informations of an organization (also means 'system')"),
			mcp.WithString("name",
				mcp.Description("Name of the organization"),
				mcp.Required(),
			),
		), h.ListUsers)
	}

	if h.Switcher.Describe {
		// 注册获取集群信息工具
		server.AddTool(mcp.NewTool(DESCRIBE_CLUSTER,
			mcp.WithDescription("Describe a cluster (Platform-scoped)"),
			mcp.WithString("cluster",
				mcp.Description("Cluster name"),
				mcp.Required(),
			),
		), h.ListClusters)

		// 注册获取组织详情工具
		server.AddTool(mcp.NewTool(DESC_ORGANIZATION,
			mcp.WithDescription("Retrive the details of an organization (also means 'system'), including the administrators."),
			mcp.WithString("name",
				mcp.Description("Name of the organization"),
				mcp.Required(),
			),
		), h.DescOrganization)
	}

}

// ListClusters 列出所有集群
func (h *PlatformHandler) ListClusters(
	ctx context.Context,
	request mcp.CallToolRequest,
) (*mcp.CallToolResult, error) {

	h.Log.Info("Listing clusters")

	clusters, err := client.GetClient().GetClusters(ctx)
	if err != nil {
		return utils.NewErrorToolResult(fmt.Sprintf("获取集群列表失败: %v", err)), nil
	}

	// 序列化为JSON字符串
	jsonData, err := json.MarshalIndent(clusters, "", "  ")
	if err != nil {
		return utils.NewErrorToolResult(fmt.Sprintf("JSON序列化失败: %v", err)), nil
	}

	h.Log.Info("Cluster listed successfully")

	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: string(jsonData),
			},
		},
	}, nil
}

// DescribeCluster 列出所有集群
func (h *PlatformHandler) DescribeCluster(
	ctx context.Context,
	request mcp.CallToolRequest,
) (*mcp.CallToolResult, error) {
	h.Log.Info("Describe cluster")

	arguments := request.Params.Arguments
	cluster, _ := arguments["cluster"].(string)

	c, err := client.GetClient().DescCluster(ctx, cluster)
	if err != nil {
		return utils.NewErrorToolResult(fmt.Sprintf("获取集群信息失败: %v", err)), nil
	}

	// 序列化为JSON字符串
	jsonData, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return utils.NewErrorToolResult(fmt.Sprintf("JSON序列化失败: %v", err)), nil
	}

	h.Log.Info("Cluster described successfully")

	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: string(jsonData),
			},
		},
	}, nil
}

// ListOrganizations 列出所有组织
func (h *PlatformHandler) ListOrganizations(
	ctx context.Context,
	request mcp.CallToolRequest,
) (*mcp.CallToolResult, error) {
	h.Log.Info("Listing organizations")

	arguments := request.Params.Arguments

	var withAdmin bool = false
	var detail bool = true
	var page int = 1
	var size int = 50

	if arguments["page"] != nil {
		pageStr := fmt.Sprintf("%v", arguments["page"])
		page64, err := strconv.ParseInt(pageStr, 10, 0)
		if err != nil {
			return utils.NewErrorToolResult(fmt.Sprintf("获取分页页码失败: %v", err)), nil
		}
		page = int(page64)
	}

	if arguments["size"] != nil {
		sizeStr := fmt.Sprintf("%v", arguments["size"])
		size64, err := strconv.ParseInt(sizeStr, 10, 0)
		if err != nil {
			return utils.NewErrorToolResult(fmt.Sprintf("获取分页大小失败: %v", err)), nil
		}
		size = int(size64)
	}

	if arguments["withAdmin"] != nil {
		adminStr := fmt.Sprintf("%v", arguments["withAdmin"])
		adminBool, err := strconv.ParseBool(adminStr)
		if err != nil {
			return utils.NewErrorToolResult(fmt.Sprintf("获取管理员详情标志失败: %v", err)), nil
		}
		withAdmin = adminBool
	}

	if arguments["detail"] != nil {
		detailStr := fmt.Sprintf("%v", arguments["detail"])
		detailBool, err := strconv.ParseBool(detailStr)
		if err != nil {
			return utils.NewErrorToolResult(fmt.Sprintf("获取组织详情标志失败: %v", err)), nil
		}
		detail = detailBool
	}

	organizations, err := client.GetClient().GetOrganizations(ctx, page, size, detail, withAdmin)
	if err != nil {
		return utils.NewErrorToolResult(fmt.Sprintf("获取组织列表失败: %v", err)), nil
	}

	// 序列化为JSON字符串
	jsonData, err := json.MarshalIndent(organizations, "", "  ")
	if err != nil {
		return utils.NewErrorToolResult(fmt.Sprintf("JSON序列化失败: %v", err)), nil
	}

	h.Log.Info("Organization listed successfully")

	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: string(jsonData),
			},
		},
	}, nil
}

// DescOrganization 获取组织详细信息
func (h *PlatformHandler) DescOrganization(
	ctx context.Context,
	request mcp.CallToolRequest,
) (*mcp.CallToolResult, error) {
	h.Log.Info("Describing organization")

	arguments := request.Params.Arguments

	if arguments["name"] == nil {
		return utils.NewErrorToolResult("The name must be filled in."), nil
	}
	name := arguments["name"].(string)

	organization, err := client.GetClient().DescOrganization(ctx, name)
	if err != nil {
		return utils.NewErrorToolResult(fmt.Sprintf("获取组织详情失败: %v", err)), nil
	}

	// 序列化为JSON字符串
	jsonData, err := json.MarshalIndent(organization, "", "  ")
	if err != nil {
		return utils.NewErrorToolResult(fmt.Sprintf("JSON序列化失败: %v", err)), nil
	}

	h.Log.Info("Organization described successfully")

	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: string(jsonData),
			},
		},
	}, nil
}

// ListUsers 列出组织用户
func (h *PlatformHandler) ListUsers(
	ctx context.Context,
	request mcp.CallToolRequest,
) (*mcp.CallToolResult, error) {
	h.Log.Info("Listing users")

	arguments := request.Params.Arguments

	if arguments["name"] == nil {
		return utils.NewErrorToolResult("The name must be filled in."), nil
	}
	name := arguments["name"].(string)

	users, err := client.GetClient().GetUsers(ctx, name)
	if err != nil {
		return utils.NewErrorToolResult(fmt.Sprintf("获取组织用户列表失败: %v", err)), nil
	}

	// 序列化为JSON字符串
	jsonData, err := json.MarshalIndent(users, "", "  ")
	if err != nil {
		return utils.NewErrorToolResult(fmt.Sprintf("JSON序列化失败: %v", err)), nil
	}

	h.Log.Info("Users listed successfully")

	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: string(jsonData),
			},
		},
	}, nil
}
