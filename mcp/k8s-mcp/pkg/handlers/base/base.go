package base

import (
	"k8s-mcp/pkg/config"
	"k8s-mcp/pkg/handlers/interfaces"
	"k8s-mcp/pkg/logger"
)

// Handler 提供公共功能
type Handler struct {
	Log      logger.Logger
	Scope    interfaces.ResourceScope
	Group    interfaces.APIGroup
	Switcher config.Switcher
}

// NewBaseHandler 创建新的基础处理程序
func NewBaseHandler(scope interfaces.ResourceScope, group interfaces.APIGroup, switcher config.Switcher) Handler {
	return Handler{
		Log:      logger.GetLogger(),
		Scope:    scope,
		Group:    group,
		Switcher: switcher,
	}
}

// GetScope 实现ToolHandler接口
func (h *Handler) GetScope() interfaces.ResourceScope {
	return h.Scope
}

// GetAPIGroup 实现ToolHandler接口
func (h *Handler) GetAPIGroup() interfaces.APIGroup {
	return h.Group
}
