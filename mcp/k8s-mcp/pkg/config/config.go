package config

// Config 应用程序配置
type Config struct {
	// 服务器配置
	Transport  string
	Port       int
	HealthPort int
	BaseURL    string
	// CORS配置
	AllowOrigins string
	// 日志配置
	LogLevel  string
	LogFormat string
	// Kubernetes配置
	Kubeconfig string
	Switcher   Switcher
}

type Switcher struct {
	List     bool
	Get      bool
	Describe bool
	Metrics  bool
	Prompt   bool
	Utility  bool
	Apply    bool
	Create   bool
	Update   bool
	Patch    bool
	Delete   bool
	Rollout  bool
}

// NewDefaultConfig 创建默认配置
func NewDefaultConfig() *Config {
	return &Config{
		Transport:    "sse",
		Port:         8080,
		HealthPort:   8081,
		BaseURL:      "",
		AllowOrigins: "*",
		LogLevel:     "info",
		LogFormat:    "console",
		Kubeconfig:   "",
		Switcher: Switcher{
			List:     true,
			Get:      true,
			Describe: true,
			Metrics:  true,
			Prompt:   true,
			Utility:  true,
			Apply:    false,
			Create:   false,
			Update:   false,
			Patch:    false,
			Delete:   false,
			Rollout:  false,
		},
	}
}
