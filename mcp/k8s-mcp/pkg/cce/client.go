package cce

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	k8sclient "k8s-mcp/pkg/client"
	"k8s-mcp/pkg/config"
	"k8s-mcp/pkg/logger"
	"k8s-mcp/pkg/models"

	corev1 "k8s.io/api/core/v1"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

// CceInterface 封装了HTTP客户端功能，用于调用外部RESTful接口
type CceInterface interface {
	GetClusters(ctx context.Context) ([]models.Cluster, error)
	DescCluster(ctx context.Context, cluster string) (models.Cluster, error)
	GetOrganizations(ctx context.Context, page int, size int, detail bool, withAdmin bool) (models.Organizations, error)
	DescOrganization(ctx context.Context, name string) (models.Organization, error)
	GetUsers(ctx context.Context, name string) ([]models.User, error)

	get(ctx context.Context, path string, headers map[string]string) (*http.Response, error)
	postJsonCtx(ctx context.Context, path string, body []byte, headers map[string]string) (*http.Response, error)
	postJson(path string, body []byte, headers map[string]string) (*http.Response, error)
	postFormCtx(ctx context.Context, path string, formData url.Values, headers map[string]string) (*http.Response, error)
	postForm(path string, formData url.Values, headers map[string]string) (*http.Response, error)
	put(ctx context.Context, path string, body []byte, headers map[string]string) (*http.Response, error)
	delete(ctx context.Context, path string, headers map[string]string) (*http.Response, error)

	getToken(sername, password string) (models.Tokens, error)
	refreshToken() (models.Tokens, error)
	validateToken() (string, error)
}

// cceClient 是CceClient的默认实现
type cceClient struct {
	CceInterface
	log     logger.Logger
	client  *http.Client
	baseURL string
	user    string
	pwd     string
	tokens  models.Tokens
}

var client CceInterface
var clientsMutex sync.RWMutex

func InitializeCceClient(cfg *config.Config) error {
	log := logger.GetLogger()
	log.Info("Initializing CCE client...")
	// Get the Kubernetes client for current context
	k8s := k8sclient.GetClient()

	// Create context
	ctx := context.Background()

	// Create object key for the secret
	secretKey := runtimeclient.ObjectKey{
		Namespace: "nccp-alert",
		Name:      "nccp-alert-secret",
	}

	// Create target secret object
	secret := &corev1.Secret{}

	// Get the secret
	err := k8s.Get(ctx, secretKey, secret)
	if err != nil {
		return fmt.Errorf("获取Secret失败: %w", err)
	}

	// Print the secret data
	user := string(secret.Data["NCCP_API_USER"])
	pwd := string(secret.Data["NCCP_API_PASSWD"])

	config, err := k8s.GetConfig().ClientConfig()
	parts := strings.Split(config.Host, ":")
	// 取第一个部分，即冒号前面的部分
	baseUrl := parts[0] + ":" + parts[1]

	c, err := NewCceClient(baseUrl, user, pwd)
	if err != nil {
		return fmt.Errorf("could not initialize cce client: %w", err)
	}
	client = c
	return err
}

// NewCceClient 创建一个新的HTTP客户端
func NewCceClient(baseURL, user, pwd string) (CceInterface, error) {
	client := &cceClient{
		client: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:       20,
				IdleConnTimeout:    180 * time.Second,
				DisableCompression: false,
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		},
		baseURL: baseURL,
		log:     logger.GetLogger(),
	}
	tokens, err := client.getToken(user, pwd)
	if err != nil {
		return nil, fmt.Errorf("could not get id token from cce: %w", err)
	}
	client.baseURL = baseURL
	client.tokens = tokens
	return client, nil
}

func GetClient() CceInterface {
	return client
}

// GetToken 通过用户名和密码获取OAuth2 token
func (h *cceClient) getToken(username, password string) (models.Tokens, error) {
	clientsMutex.Lock()
	defer clientsMutex.Unlock()

	formData := url.Values{}
	formData.Set("grant_type", "password")
	formData.Set("response_type", "code")
	formData.Set("client_id", "cce-client")
	formData.Set("scope", "openid")
	formData.Set("username", username)
	formData.Set("password", password)

	var result models.Tokens
	headers := map[string]string{
		"Organization": "nccpmanager",
		"Content-Type": "application/x-www-form-urlencoded",
	}
	resp, err := h.postForm("/auth/realms/CCE/protocol/openid-connect/token", formData, headers)
	if err != nil {
		return result, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return result, fmt.Errorf("failed to get token: %s", resp.Status)
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return result, fmt.Errorf("failed to decode response: %v", err)
	}
	result.AuthorizedAt = uint64(time.Now().UnixMilli())
	h.user = username
	h.pwd = password
	result.BeaerToken = result.TokenType + " " + result.IdToken
	return result, nil
}

// GetToken 通过用户名和密码获取OAuth2 token
func (h *cceClient) refreshToken() (models.Tokens, error) {
	clientsMutex.Lock()
	defer clientsMutex.Unlock()

	formData := url.Values{}
	formData.Set("grant_type", "refresh_token")
	formData.Set("response_type", "code")
	formData.Set("client_id", "cce-client")
	formData.Set("refresh_token", h.tokens.RefreshToken)

	var result models.Tokens
	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}
	resp, err := h.postForm("/auth/realms/CCE/protocol/openid-connect/token", formData, headers)
	if err != nil {
		return result, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return result, fmt.Errorf("failed to get token: %s", resp.Status)
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return result, fmt.Errorf("failed to decode response: %v", err)
	}
	result.AuthorizedAt = uint64(time.Now().UnixMilli())
	result.BeaerToken = result.TokenType + " " + result.IdToken
	return result, nil
}

// validateToken 检查token是否过期，如过期则进行刷新，返回最新token
func (h *cceClient) validateToken() (string, error) {
	if (h.tokens == models.Tokens{}) {
		return "", fmt.Errorf("tokens are not initialized")
	}
	now := uint64(time.Now().UnixMilli())
	expireTime := h.tokens.AuthorizedAt + h.tokens.ExpiresIn*1000
	closeToExpire := expireTime - 600000 // 过期前10分钟
	if now < closeToExpire {             // 距离过期还有较长时间
		return h.tokens.BeaerToken, nil
	}
	if now >= closeToExpire && now < expireTime { // 接近过期，需要进行刷新
		h.log.Warn("Token closeto expired: createdAt=%s, expiresIn=%d, now=%d",
			h.tokens.AuthorizedAt, h.tokens.ExpiresIn, now)
		t, err := h.refreshToken()
		if err != nil {
			return "", fmt.Errorf("failed to get new token: %v", err)
		}
		h.log.Info("Token refreshed.")
		h.tokens = t
		return h.tokens.BeaerToken, nil
	}
	if now > expireTime { // 已过期，重新认证
		h.log.Warn("Token closeto expired: createdAt=%s, expiresIn=%d, now=%d",
			h.tokens.AuthorizedAt, h.tokens.ExpiresIn, now)
		t, err := h.getToken(h.user, h.pwd)
		if err != nil {
			return "", fmt.Errorf("failed to refresh token: %v", err)
		}
		h.log.Info("Token renewed.")
		h.tokens = t
		return h.tokens.BeaerToken, nil
	}
	return "", fmt.Errorf("failed to validate token")
}

// get 执行HTTP GET请求
func (h *cceClient) get(ctx context.Context, path string, headers map[string]string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, h.baseURL+path, nil)
	if err != nil {
		return nil, err
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}
	return h.client.Do(req)
}

// postJson 执行HTTP POST请求
func (h *cceClient) postJsonCtx(ctx context.Context, path string, body []byte, headers map[string]string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, h.baseURL+path, bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")
	return h.client.Do(req)
}

// postJson 执行HTTP POST请求
func (h *cceClient) postJson(path string, body []byte, headers map[string]string) (*http.Response, error) {
	req, err := http.NewRequest(http.MethodPost, h.baseURL+path, bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")
	return h.client.Do(req)
}

// postForm 执行HTTP Form POST请求
func (h *cceClient) postFormCtx(ctx context.Context, path string, formData url.Values, headers map[string]string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, h.baseURL+path, bytes.NewBufferString(formData.Encode()))
	if err != nil {
		return nil, err
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	return h.client.Do(req)
}

// postForm 执行HTTP Form POST请求
func (h *cceClient) postForm(path string, formData url.Values, headers map[string]string) (*http.Response, error) {
	req, err := http.NewRequest(http.MethodPost, h.baseURL+path, bytes.NewBufferString(formData.Encode()))
	if err != nil {
		return nil, err
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	return h.client.Do(req)
}

// put 执行HTTP PUT请求
func (h *cceClient) put(ctx context.Context, path string, body []byte, headers map[string]string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodPut, h.baseURL+path, bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")
	return h.client.Do(req)
}

// delete 执行HTTP DELETE请求
func (h *cceClient) delete(ctx context.Context, path string, headers map[string]string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodDelete, h.baseURL+path, nil)
	if err != nil {
		return nil, err
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}
	return h.client.Do(req)
}

// GetClusters 获取集群列表
func (h *cceClient) GetClusters(ctx context.Context) ([]models.Cluster, error) {
	token, err := h.validateToken()
	if err != nil {
		return nil, fmt.Errorf("failed to get clusters: %v", err)
	}
	headers := map[string]string{
		"Authorization": token,
	}
	resp, err := h.get(ctx, "/api/v3/clusters", headers)
	if err != nil {
		return nil, fmt.Errorf("failed to get clusters: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get clusters: %s", resp.Status)
	}

	var result models.ClusterListResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode clusters response: %v", err)
	}
	return result.Items, nil
}

// DescCluster 获取集群列表
func (h *cceClient) DescCluster(ctx context.Context, cluster string) (models.Cluster, error) {
	var c models.Cluster

	token, err := h.validateToken()
	if err != nil {
		return c, fmt.Errorf("failed to get clusters: %v", err)
	}
	headers := map[string]string{
		"Authorization": token,
	}
	resp, err := h.get(ctx, "/api/v3/clusters/"+cluster, headers)
	if err != nil {
		return c, fmt.Errorf("failed to get cluster: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return c, fmt.Errorf("failed to get cluster: %s", resp.Status)
	}
	if err := json.NewDecoder(resp.Body).Decode(&c); err != nil {
		return c, fmt.Errorf("failed to decode cluster response: %v", err)
	}
	return c, nil
}

// GetOrganizations 获取组织列表
func (h *cceClient) GetOrganizations(ctx context.Context, page int, size int, detail bool, withAdmin bool) (models.Organizations, error) {
	var result models.Organizations

	token, err := h.validateToken()
	if err != nil {
		return result, fmt.Errorf("failed to get organizations: %v", err)
	}

	headers := map[string]string{
		"Authorization": token,
	}

	path := fmt.Sprintf("/apis/cce/v1/organizations?page=%d&size=%d&detail=%t&with_admins=%t",
		page, size, detail, withAdmin)
	resp, err := h.get(ctx, path, headers)

	if err != nil {
		return result, fmt.Errorf("failed to get organizations: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return result, fmt.Errorf("failed to get organizations: %s", resp.Status)
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return result, fmt.Errorf("failed to decode organizations response: %v", err)
	}
	return result, nil
}

// DescOrganization 获取组织详细信息
func (h *cceClient) DescOrganization(ctx context.Context, name string) (models.Organization, error) {
	var orgs models.Organizations
	var result models.Organization

	token, err := h.validateToken()
	if err != nil {
		return result, fmt.Errorf("failed to get organization: %v", err)
	}

	headers := map[string]string{
		"Authorization": token,
	}

	path := fmt.Sprintf("/apis/cce/v1/organizations?name=%s&detail=true&with_admins=true", name)
	resp, err := h.get(ctx, path, headers)

	if err != nil {
		return result, fmt.Errorf("failed to get organization: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return result, fmt.Errorf("failed to get organizations: %s", resp.Status)
	}

	if err := json.NewDecoder(resp.Body).Decode(&orgs); err != nil {
		return result, fmt.Errorf("failed to decode organization response: %v", err)
	}

	if len(orgs.Items) > 0 {
		result = orgs.Items[0]
		return result, nil
	} else {
		return result, fmt.Errorf("Organization '%s' not found.")
	}

}

// GetUsers 获取组织的用户列表
func (h *cceClient) GetUsers(ctx context.Context, name string) ([]models.User, error) {
	var users models.Users
	var user []models.User

	token, err := h.validateToken()
	if err != nil {
		return nil, fmt.Errorf("failed to get organizations: %v", err)
	}

	headers := map[string]string{
		"Authorization": token,
	}

	path := fmt.Sprintf("/apis/cce/v1/organizations/%s/users", name)
	resp, err := h.get(ctx, path, headers)

	if err != nil {
		return nil, fmt.Errorf("failed to get users: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get users: %s", resp.Status)
	}

	if err := json.NewDecoder(resp.Body).Decode(&users); err != nil {
		return nil, fmt.Errorf("failed to decode organizations response: %v", err)
	}

	user = users.Items
	return user, nil
}
