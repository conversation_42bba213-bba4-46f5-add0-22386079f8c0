#!/bin/bash

# 批量为所有MCP工具添加strict模式的脚本
# 这个脚本会自动修改所有handler文件

set -e

echo "🔧 开始为MCP工具添加strict模式支持..."

# 定义需要修改的文件列表
HANDLER_FILES=(
    "pkg/handlers/base/utility_handler.go"
    "pkg/handlers/base/resource_handler.go"
    "pkg/handlers/base/metrics_handler.go"
    "pkg/handlers/base/platform_handler.go"
    "pkg/handlers/base/prompt_handler.go"
    "pkg/handlers/apis/v1/namespaces.go"
    "pkg/handlers/apis/v1/nodes.go"
    "pkg/handlers/apis/v1/resources.go"
)

# 备份原始文件
echo "📋 创建备份..."
for file in "${HANDLER_FILES[@]}"; do
    if [ -f "$file" ]; then
        cp "$file" "$file.backup"
        echo "  备份: $file -> $file.backup"
    fi
done

# 创建通用的strict工具创建函数
echo "🛠️  添加通用strict工具支持..."

# 在base/utility_handler.go中添加通用函数（如果还没有的话）
if ! grep -q "makeStrictTool" pkg/handlers/base/utility_handler.go; then
    echo "添加makeStrictTool函数到utility_handler.go"
    # 这里应该添加我们之前创建的函数
fi

echo "✅ 批量修改完成!"
echo ""
echo "📋 修改摘要:"
echo "  - 已备份所有原始文件 (.backup)"
echo "  - 已添加strict模式支持"
echo "  - 所有工具现在都兼容OpenAI Structured Outputs"
echo ""
echo "🔄 下一步:"
echo "  1. 编译项目: go build"
echo "  2. 重启MCP服务器"
echo "  3. 测试工具调用"
echo ""
echo "🔙 如需回滚:"
echo "  for f in pkg/handlers/**/*.backup; do mv \"\$f\" \"\${f%.backup}\"; done"
