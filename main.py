import asyncio
import os
from dotenv import load_dotenv
from mcp_use import MCPAgent, MCPClient
from llm_config import create_k8s_llm, print_provider_status, get_current_provider


async def main():
    """Run the example using a configuration file."""
    # Load environment variables
    load_dotenv()

    # 打印当前LLM提供商状态
    print("=" * 60)
    print("🚀 MCP Agent 启动")
    print("=" * 60)
    print_provider_status()
    print("=" * 60)

    config = {
        "mcpServers": {
            "fetch": {
                "type": "sse",
                "url": "https://mcp.api-inference.modelscope.net/da81fcffd39044/sse"
            }
        }
    }

    # Create MCPClient from config file
    client = MCPClient.from_dict(config)

    # 根据配置创建LLM实例
    # 自动根据 .env 中的 LLM_PROVIDER 选择提供商
    print(f"🔧 正在初始化 {get_current_provider().upper()} LLM...")
    llm = create_k8s_llm("production")  # 使用生产级K8s配置

    print(f"✅ LLM初始化完成")
    print(f"   模型: {llm.model}")
    print(f"   温度: {llm.temperature}")
    print(f"   最大Token: {llm.max_tokens}")
    print(f"   服务地址: {llm.base_url}")
    print("=" * 60)

    # Create agent with the client
    agent = MCPAgent(llm=llm, client=client, max_steps=30)

    # Run the query
    print("🔍 开始执行任务...")
    result = await agent.run(
        "fetch page https://www.ruanyifeng.com/blog/",
        max_steps=30,
    )
    print(f"\n✅ 任务完成!")
    print(f"📋 结果: {result}")

if __name__ == "__main__":
    # Run the appropriate example
    asyncio.run(main())
